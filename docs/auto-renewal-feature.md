# BTFS 自动续费功能

## 概述

BTFS 4.0 引入了智能自动续费功能，为用户提供无缝的文件存储体验。该功能可以自动监控文件的存储期限，并在到期前自动执行续费操作，确保重要数据的持续可用性，无需用户手动干预。

自动续费功能通过后台服务持续运行，定期检查用户文件的存储状态，并根据预设配置自动处理续费流程，为企业级应用和个人用户提供可靠的数据保护。

## 核心特性

### 智能监控
- **自动检测**: 系统每小时自动检查文件存储期限
- **提前预警**: 在文件到期前24小时触发续费流程
- **状态跟踪**: 实时监控续费状态和执行结果

### 灵活配置
- **个性化设置**: 支持为每个文件单独配置续费参数
- **多种续费周期**: 支持自定义续费时长（以天为单位）
- **价格控制**: 可设置最大续费价格限制

### 安全可靠
- **自动恢复**: 服务异常时自动重启和恢复
- **错误处理**: 完善的错误处理和重试机制
- **日志记录**: 详细的操作日志便于问题排查

## 架构设计

### 服务组件

#### AutoRenewalService
核心自动续费服务，负责：
- 定期扫描需要续费的文件
- 执行自动续费操作
- 管理续费配置和状态

#### ServiceManager
服务管理器，提供：
- 服务生命周期管理
- 服务状态监控
- 持久化状态保存

#### RenewalInfo
续费配置信息，包含：
- 文件CID和分片信息
- 续费周期和价格设置
- 启用状态和时间戳

### 工作流程

```
1. 服务启动 → 2. 定期检查 → 3. 识别到期文件 → 4. 执行续费 → 5. 更新状态
     ↑                                                                    ↓
     ←←←←←←←←←←←←←←←←←←← 6. 记录日志 ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

## 使用指南

### 启动自动续费服务

```bash
# 启动自动续费服务
btfs storage upload renew service start

# 检查服务状态
btfs storage upload renew service status

# 停止服务
btfs storage upload renew service stop

# 重启服务
btfs storage upload renew service restart
```

### 配置文件自动续费

#### 上传时启用自动续费

```bash
# 上传文件并启用自动续费
btfs add myfile.txt --auto-renew=true --storage-length=30

# 指定续费价格和代币类型
btfs storage upload myfile.txt --auto-renew=true --price=1000 --token=WBTT
```

#### 为已上传文件启用自动续费

```bash
# 为指定文件启用自动续费
btfs storage upload renew enable <file-hash>

# 禁用自动续费
btfs storage upload renew disable <file-hash>

# 查看自动续费状态
btfs storage upload renew status <file-hash>
```

### 配置参数说明

| 参数 | 类型 | 说明 | 默认值 |
|------|------|------|--------|
| `auto-renew` | boolean | 是否启用自动续费 | false |
| `renewal-duration` | int | 续费周期（天） | 30 |
| `max-price` | int64 | 最大续费价格（µBTT） | 当前市场价 |
| `token` | string | 支付代币类型 | WBTT |

## API 接口

### 服务管理接口

#### 启动服务
```http
POST /api/v0/storage/upload/renew/service/start
```

#### 停止服务
```http
POST /api/v0/storage/upload/renew/service/stop
```

#### 服务状态
```http
GET /api/v0/storage/upload/renew/service/status
```

### 文件续费管理

#### 启用自动续费
```http
POST /api/v0/storage/upload/renew/enable
Content-Type: application/json

{
  "file_hash": "QmXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "renewal_duration": 30,
  "max_price": 1000
}
```

#### 禁用自动续费
```http
POST /api/v0/storage/upload/renew/disable
Content-Type: application/json

{
  "file_hash": "QmXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
}
```

#### 查询续费状态
```http
GET /api/v0/storage/upload/renew/status?file-hash=QmXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX
```

**响应示例:**
```json
{
  "cid": "QmXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "enabled": true,
  "renewal_duration": 30,
  "price": 1000,
  "token": "0x...",
  "next_renewal_at": "2024-02-15T10:30:00Z",
  "last_renewal_at": "2024-01-15T10:30:00Z",
  "total_pay": 50000,
  "shards_info": [
    {
      "shard_id": "QmYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY",
      "sp_id": "12D3KooW...",
      "contract_id": "contract_123",
      "shard_size": 1048576
    }
  ]
}
```

## 最佳实践

### 企业级部署建议

1. **服务监控**: 建议配置服务监控告警，确保自动续费服务正常运行
2. **余额管理**: 定期检查账户余额，确保有足够资金进行自动续费
3. **价格策略**: 根据业务需求合理设置最大续费价格
4. **备份策略**: 重要文件建议同时配置多种备份策略

### 性能优化

1. **批量操作**: 对于大量文件，建议分批配置自动续费
2. **网络优化**: 确保节点网络连接稳定，避免续费失败
3. **资源配置**: 为自动续费服务分配足够的系统资源

## 故障排除

### 常见问题

#### 服务无法启动
```bash
# 检查节点状态
btfs id

# 检查存储客户端是否启用
btfs config Experimental.StorageClientEnabled

# 查看服务日志
btfs log tail
```

#### 续费失败
- 检查账户余额是否充足
- 验证网络连接状态
- 确认存储提供商在线状态
- 检查价格设置是否合理

#### 配置丢失
- 检查数据存储完整性
- 重新配置自动续费参数
- 验证文件状态

### 日志分析

自动续费服务提供详细的日志信息：

```bash
# 查看续费相关日志
btfs log tail | grep "auto-renewal"

# 查看特定文件的续费日志
btfs log tail | grep "QmXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX"
```

## 注意事项

1. **余额要求**: 确保账户有足够的WBTT余额支付续费费用
2. **网络稳定**: 自动续费需要稳定的网络连接
3. **服务依赖**: 自动续费依赖存储客户端功能，需确保相关服务正常运行
4. **价格波动**: 存储价格可能波动，建议设置合理的最大价格限制

---

*更新时间: 2024年1月*

*如需更多帮助，请访问 [BTFS社区](https://discord.gg/btfs) 获取技术支持。*
