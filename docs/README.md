# Developer Documentation and Guides

Please check out the following guide to using and developing BTFS:

[BTFS Developer Hub](https://docs.btfs.io/)

## Guides

- [How to Implement an API Client](implement-api-bindings.md)
- [Connecting with Websockets](transports.md) — if you want `js-ipfs` nodes in web browsers to connect to your `go-ipfs` node, you will need to turn on websocket support in your `go-ipfs` node.

## Advanced User Guides

- [Configuration reference](config.md)
    - [Datastore configuration](datastores.md)

## Developing `go-btfs`

- [Performance Debugging Guidelines](debug-guide.md)

## Other

- Our [Developer Certificate of Origin (DCO)](developer-certificate-of-origin) — when you sign your commits with `Signed-off-by: <your name>`, you are agreeing to this document.
