# BTFS 代理上传功能

## 概述

BTFS 4.0 引入了创新的代理上传功能，允许用户通过代理节点上传文件到分布式存储网络。该功能为用户提供了更灵活的上传方式，特别适用于网络条件受限、计算资源不足或需要专业服务的场景。

代理上传功能通过建立用户与代理节点之间的信任机制，实现文件的安全、高效上传，同时保持BTFS网络的去中心化特性。

## 核心特性

### 灵活的上传方式
- **代理服务**: 通过专业代理节点处理文件上传
- **按需付费**: 根据实际使用量支付代理服务费用
- **多代理支持**: 可选择不同的代理节点提供服务

### 安全可靠
- **加密传输**: 文件在传输过程中采用端到端加密
- **身份验证**: 严格的身份验证机制确保交易安全
- **资金托管**: 智能合约管理资金，确保交易公平

### 高效便捷
- **自动化流程**: 简化的上传流程，减少用户操作复杂度
- **实时监控**: 实时跟踪上传进度和状态
- **快速响应**: 优化的网络路由提供更快的上传速度

## 架构设计

### 系统组件

#### 代理节点 (Proxy Node)
- 接收用户上传请求
- 处理文件分片和编码
- 与存储提供商协商存储合约
- 管理支付和结算流程

#### 客户端 (Client)
- 发起代理上传请求
- 管理支付和余额
- 监控上传状态

#### 智能合约
- 管理代理服务配置
- 处理支付和结算
- 记录交易历史

### 工作流程

```
1. 客户端请求 → 2. 代理响应 → 3. 支付确认 → 4. 文件上传 → 5. 存储确认
     ↑                                                                    ↓
     ←←←←←←←←←←←←←←←← 6. 完成通知 ←←←←←←←←←←←←←←←←←←←←←←←←←←←←←
```

## 使用指南

### 代理节点配置

#### 启用代理模式

```bash
# 在配置文件中启用代理模式
btfs config Experimental.EnableProxyMode true

# 重启节点使配置生效
btfs shutdown
btfs daemon
```

#### 设置代理服务价格

```bash
# 设置代理服务价格 (单位: BTT)
btfs storage upload proxy config --proxy-price=100

# 查看当前代理配置
btfs storage upload proxy config show
```

**响应示例:**
```json
{
  "price": 100
}
```

### 客户端使用

#### 通过代理上传文件

```bash
# 使用指定代理节点上传文件
btfs storage upload myfile.txt --proxy=12D3KooWXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX

# 指定存储参数
btfs storage upload myfile.txt \
  --proxy=12D3KooWXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX \
  --storage-length=30 \
  --price=1000
```

#### 支付代理服务费用

```bash
# 向代理节点支付费用
btfs storage upload proxy pay 12D3KooWXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX 100 --cid=QmYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY

# 查看支付余额
btfs storage upload proxy pay balance 0x1234567890123456789012345678901234567890
```

#### 通知代理执行上传

```bash
# 通知代理节点执行上传
btfs storage upload proxy notify 12D3KooWXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX QmYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY
```

#### 查看代理上传的文件列表

```bash
# 列出通过代理上传的文件
btfs storage upload proxy list
```

**响应示例:**
```json
[
  {
    "from": "0x1234567890123456789012345678901234567890",
    "cid": "QmYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY",
    "file_size": 1048576,
    "price": 100,
    "total_pay": "50000000000000000000",
    "created_at": 1640995200,
    "expire_at": 1643673600
  }
]
```

## API 接口

### 代理节点接口

#### 接收上传请求
```http
POST /api/v0/storage/upload/proxy
Content-Type: application/json

{
  "file_hash": "QmYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY"
}
```

**响应:**
```json
{
  "proxy_address": "0x9876543210987654321098765432109876543210",
  "need_pay": "50000000000000000000",
  "file_size": 1048576,
  "price": 100
}
```

#### 配置代理服务
```http
POST /api/v0/storage/upload/proxy/config
Content-Type: application/json

{
  "proxy_price": 100
}
```

#### 查看代理配置
```http
GET /api/v0/storage/upload/proxy/config/show
```

### 客户端接口

#### 支付代理费用
```http
POST /api/v0/storage/upload/proxy/pay
Content-Type: application/json

{
  "proxy_id": "12D3KooWXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "amount": "100",
  "cid": "QmYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY"
}
```

#### 查询余额
```http
GET /api/v0/storage/upload/proxy/pay/balance?address=0x1234567890123456789012345678901234567890
```

#### 通知代理上传
```http
POST /api/v0/storage/upload/proxy/notify
Content-Type: application/json

{
  "proxy_id": "12D3KooWXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXXX",
  "cid": "QmYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYYY"
}
```

#### 查看上传文件列表
```http
GET /api/v0/storage/upload/proxy/list
```

## 支付机制

### 支付流程

1. **预支付**: 客户端向代理节点的钱包地址转账
2. **余额管理**: 代理节点管理客户端余额
3. **服务扣费**: 上传完成后从余额中扣除相应费用
4. **退款机制**: 上传失败时自动退款

### 费用计算

代理服务费用基于以下因素计算：
- 文件大小
- 存储时长
- 当前网络价格
- 代理服务费率

```
总费用 = (文件大小 × 存储价格 × 存储天数) + 代理服务费
```

### 支付代币

支持多种代币支付：
- **WBTT**: 主要支付代币
- **TRX**: TRON原生代币
- **USDD**: TRON生态稳定币
- **USDT**: 泰达币

## 安全机制

### 身份验证
- **数字签名**: 所有交易都需要数字签名验证
- **对等身份**: 基于libp2p的对等身份验证
- **合约验证**: 智能合约验证交易合法性

### 资金安全
- **托管机制**: 资金在智能合约中托管
- **超时保护**: 设置支付超时机制防止资金锁定
- **自动退款**: 服务失败时自动退款

### 数据安全
- **端到端加密**: 文件传输过程全程加密
- **分片存储**: 文件分片存储提高安全性
- **冗余备份**: 多副本存储确保数据可靠性

## 最佳实践

### 代理节点运营

1. **硬件配置**: 建议使用高性能服务器提供稳定服务
2. **网络优化**: 确保良好的网络连接和带宽
3. **价格策略**: 根据市场情况合理定价
4. **服务监控**: 实时监控服务状态和性能

### 客户端使用

1. **代理选择**: 选择信誉良好、性能稳定的代理节点
2. **余额管理**: 保持足够余额确保服务连续性
3. **文件备份**: 重要文件建议多重备份
4. **成本控制**: 合理规划存储需求控制成本

## 故障排除

### 常见问题

#### 代理模式无法启用
```bash
# 检查配置
btfs config Experimental.EnableProxyMode

# 检查节点状态
btfs id

# 重启节点
btfs shutdown && btfs daemon
```

#### 支付失败
- 检查钱包余额
- 验证代理节点地址
- 确认网络连接状态
- 检查交易gas费用

#### 上传超时
- 检查文件大小和网络状况
- 验证代理节点在线状态
- 重试上传操作

### 日志分析

```bash
# 查看代理相关日志
btfs log tail | grep "proxy"

# 查看支付相关日志
btfs log tail | grep "payment"

# 查看上传状态日志
btfs log tail | grep "upload"
```

## 注意事项

1. **代理选择**: 选择可信的代理节点，避免资金损失
2. **网络费用**: 注意区块链网络的gas费用
3. **服务可用性**: 代理服务可能因网络或节点问题暂时不可用
4. **数据隐私**: 虽然有加密保护，但仍需谨慎处理敏感数据

---

*更新时间: 2024年1月*

*如需更多帮助，请访问 [BTFS社区](https://discord.gg/btfs) 获取技术支持。*
