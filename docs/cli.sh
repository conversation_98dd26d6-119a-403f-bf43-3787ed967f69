#!/bin/bash
# Updates `content/reference/api/cli.md` from the locally installed go-btfs

exec &> ./content/reference/api/cli.md

echo "---
title: \"CLI Commands\"
identifier: clicommands
weight: 10
menu:
    reference:
        parent: api
---

<!-- TODO: Remove index since it's handled by the TOC in the menu? -->
<!-- TODO: Structure this around command groups (dag, object, files, etc.) -->

**Note:** btfs can run in either “online” (you have btfs running separately as a daemon process) or “offline” mode, but some commands are only supported when online. For example \`btfs swarm peers\` only works in online mode because you won’t be connected to a swarm at all if you’re offline. For more about running btfs as a daemon, see [“going online” in the usage documentation]({{< relref \"usage.md#going-online\" >}}).

This document is autogenerated from <a href='https://github.com/bittorrent/go-btfs/tree/master/docs/cli.sh'>scripts/cli.sh</a>. For issues and support, check out the <a href='https://github.com/bittorrent/go-btfs/tree/master/docs'>docs repository</a> on GitHub.
"

echo "*Generated on $(date +"%Y-%m-%d %T"), from go-btfs $(btfs version -n).*"
printf "\n"
btfs commands | while read line ; do
  echo "- [$line](#$(echo $line | tr -s ' ' -))"
done
printf "\n"
btfs commands | while read line ; do
  printf "## $line\n\n\`\`\`\n"
  $line --help
  printf "\`\`\`\n\n"
done

