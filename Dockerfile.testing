FROM golang:1.23
MAINTAINER TRON-US <<EMAIL>>

# Install deps
RUN apt-get update && apt-get install -y \
  libssl-dev \
  ca-certificates \
  fuse

# Dockerfile.testing will build an image that contains the go-btfs source and binary.
# It is quite large.  Its primary use case is to run the unit tests and test/sharness tests.
# Use Dockerfile to run a btfs daemon instead
ENV SRC_DIR /go-btfs
ENV TEST_NO_FUSE 1

# Download packages first so they can be cached.
COPY go.mod go.sum $SRC_DIR/
RUN cd $SRC_DIR \
  && go mod download

COPY . $SRC_DIR

# Newer git submodule uses "absorbgitdirs" option by default which does not
# include .git folder inside a submodule.
# Use a build time variable $gitdir to specify the location of the actual .git folder.
ARG gitdir=.git
RUN test -d $SRC_DIR/.git \
  || mv $SRC_DIR/$gitdir $SRC_DIR/.git

# Preload an in-tree but disabled-by-default plugin by adding it to the IPFS_PLUGINS variable
# e.g. docker build --build-arg IPFS_PLUGINS="foo bar baz"
ARG IPFS_PLUGINS

# Build the thing.
# Also: fix getting HEAD commit hash via git rev-parse.
RUN cd $SRC_DIR \
  && mkdir .git/objects \
  && GOFLAGS=-buildvcs=false make build GOTAGS=openssl IPFS_PLUGINS=$IPFS_PLUGINS

# Get su-exec, a very minimal tool for dropping privileges,
# and tini, a very minimal init daemon for containers
ENV SUEXEC_VERSION v0.2
ENV TINI_VERSION v0.19.0
RUN set -eux; \
    dpkgArch="$(dpkg --print-architecture)"; \
    case "${dpkgArch##*-}" in \
        "amd64" | "armhf" | "arm64") tiniArch="tini-$dpkgArch" ;;\
        *) echo >&2 "unsupported architecture: ${dpkgArch}"; exit 1 ;; \
    esac; \
  cd /tmp \
  && git clone https://github.com/ncopa/su-exec.git \
  && cd su-exec \
  && git checkout -q $SUEXEC_VERSION \
  && make \
  && cd /tmp \
  && wget -q -O tini https://github.com/krallin/tini/releases/download/$TINI_VERSION/$tiniArch \
  && chmod +x tini

# Do this in the current container
RUN mv /tmp/su-exec/su-exec /sbin/su-exec
RUN mv /bin/fusermount /usr/local/bin/fusermount

# Add suid bit on fusermount so it will run properly
RUN chmod 4755 /usr/local/bin/fusermount

# Fix permissions on start_btfs (ignore the build machine's permissions)
RUN chmod 0755 /usr/local/bin/start_btfs

# Create the fs-repo directory and switch to a non-privileged user.
ENV BTFS_PATH /data/btfs
RUN mkdir -p $BTFS_PATH \
  && adduser -D -h $BTFS_PATH -u 1000 -G users btfs \
  && chown btfs:users $BTFS_PATH

# Create mount points for `btfs mount` command
RUN mkdir /btfs /btns \
  && chown btfs:users /btfs /btns

# Change owner of go-btfs source and go folder
RUN chown -R btfs:users /go \ 
  && chown -R btfs:users /go-btfs

# Expose the fs-repo as a volume.
# start_btfs initializes an fs-repo if none is mounted.
# Important this happens after the USER directive so permission are correct.
VOLUME $BTFS_PATH

# The default logging level
ENV BTFS_LOGGING ""
ENV PATH="/usr/local/go/bin:${PATH}"

# Commands are run as user btfs not root
# Comment line below out if you want to run as root
ENTRYPOINT ["/sbin/su-exec", "btfs:1000"]

# by default lets run the make test
CMD cd /go-btfs && make test 
