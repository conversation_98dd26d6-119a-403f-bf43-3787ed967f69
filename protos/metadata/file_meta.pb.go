// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: file_meta.proto

package metadata

import (
	fmt "fmt"
	proto "github.com/golang/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.ProtoPackageIsVersion3 // please upgrade the proto package

type Contract_ContractStatus int32

const (
	Contract_INVALID   Contract_ContractStatus = 0
	Contract_INIT      Contract_ContractStatus = 1
	Contract_COMPLETED Contract_ContractStatus = 2
	Contract_CLOSED    Contract_ContractStatus = 3
)

var Contract_ContractStatus_name = map[int32]string{
	0: "INVALID",
	1: "INIT",
	2: "COMPLETED",
	3: "CLOSED",
}

var Contract_ContractStatus_value = map[string]int32{
	"INVALID":   0,
	"INIT":      1,
	"COMPLETED": 2,
	"CLOSED":    3,
}

func (x Contract_ContractStatus) String() string {
	return proto.EnumName(Contract_ContractStatus_name, int32(x))
}

func (Contract_ContractStatus) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_9b47804ec1b8d60b, []int{1, 0}
}

type ContractMeta struct {
	ContractId           string   `protobuf:"bytes,1,opt,name=contract_id,json=contractId,proto3" json:"contract_id,omitempty"`
	SpId                 string   `protobuf:"bytes,2,opt,name=sp_id,json=spId,proto3" json:"sp_id,omitempty"`
	UserId               string   `protobuf:"bytes,3,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	ShardHash            string   `protobuf:"bytes,4,opt,name=shard_hash,json=shardHash,proto3" json:"shard_hash,omitempty"`
	ShardIndex           uint64   `protobuf:"varint,5,opt,name=shard_index,json=shardIndex,proto3" json:"shard_index,omitempty"`
	ShardSize            uint64   `protobuf:"varint,6,opt,name=shard_size,json=shardSize,proto3" json:"shard_size,omitempty"`
	Price                uint64   `protobuf:"varint,7,opt,name=price,proto3" json:"price,omitempty"`
	Amount               uint64   `protobuf:"varint,8,opt,name=amount,proto3" json:"amount,omitempty"`
	Token                string   `protobuf:"bytes,9,opt,name=token,proto3" json:"token,omitempty"`
	AutoRenewal          bool     `protobuf:"varint,10,opt,name=auto_renewal,json=autoRenewal,proto3" json:"auto_renewal,omitempty"`
	StorageStart         uint64   `protobuf:"varint,11,opt,name=storage_start,json=storageStart,proto3" json:"storage_start,omitempty"`
	StorageEnd           uint64   `protobuf:"varint,12,opt,name=storage_end,json=storageEnd,proto3" json:"storage_end,omitempty"`
	XXX_NoUnkeyedLiteral struct{} `json:"-"`
	XXX_unrecognized     []byte   `json:"-"`
	XXX_sizecache        int32    `json:"-"`
}

func (m *ContractMeta) Reset()         { *m = ContractMeta{} }
func (m *ContractMeta) String() string { return proto.CompactTextString(m) }
func (*ContractMeta) ProtoMessage()    {}
func (*ContractMeta) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b47804ec1b8d60b, []int{0}
}
func (m *ContractMeta) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ContractMeta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ContractMeta.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ContractMeta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ContractMeta.Merge(m, src)
}
func (m *ContractMeta) XXX_Size() int {
	return m.Size()
}
func (m *ContractMeta) XXX_DiscardUnknown() {
	xxx_messageInfo_ContractMeta.DiscardUnknown(m)
}

var xxx_messageInfo_ContractMeta proto.InternalMessageInfo

func (m *ContractMeta) GetContractId() string {
	if m != nil {
		return m.ContractId
	}
	return ""
}

func (m *ContractMeta) GetSpId() string {
	if m != nil {
		return m.SpId
	}
	return ""
}

func (m *ContractMeta) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *ContractMeta) GetShardHash() string {
	if m != nil {
		return m.ShardHash
	}
	return ""
}

func (m *ContractMeta) GetShardIndex() uint64 {
	if m != nil {
		return m.ShardIndex
	}
	return 0
}

func (m *ContractMeta) GetShardSize() uint64 {
	if m != nil {
		return m.ShardSize
	}
	return 0
}

func (m *ContractMeta) GetPrice() uint64 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *ContractMeta) GetAmount() uint64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *ContractMeta) GetToken() string {
	if m != nil {
		return m.Token
	}
	return ""
}

func (m *ContractMeta) GetAutoRenewal() bool {
	if m != nil {
		return m.AutoRenewal
	}
	return false
}

func (m *ContractMeta) GetStorageStart() uint64 {
	if m != nil {
		return m.StorageStart
	}
	return 0
}

func (m *ContractMeta) GetStorageEnd() uint64 {
	if m != nil {
		return m.StorageEnd
	}
	return 0
}

type Contract struct {
	Meta                 *ContractMeta           `protobuf:"bytes,1,opt,name=meta,proto3" json:"meta,omitempty"`
	SpSignature          []byte                  `protobuf:"bytes,2,opt,name=sp_signature,json=spSignature,proto3" json:"sp_signature,omitempty"`
	UserSignature        []byte                  `protobuf:"bytes,3,opt,name=user_signature,json=userSignature,proto3" json:"user_signature,omitempty"`
	CreateTime           uint64                  `protobuf:"varint,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	Status               Contract_ContractStatus `protobuf:"varint,5,opt,name=status,proto3,enum=filemeta.Contract_ContractStatus" json:"status,omitempty"`
	XXX_NoUnkeyedLiteral struct{}                `json:"-"`
	XXX_unrecognized     []byte                  `json:"-"`
	XXX_sizecache        int32                   `json:"-"`
}

func (m *Contract) Reset()         { *m = Contract{} }
func (m *Contract) String() string { return proto.CompactTextString(m) }
func (*Contract) ProtoMessage()    {}
func (*Contract) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b47804ec1b8d60b, []int{1}
}
func (m *Contract) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Contract) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Contract.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Contract) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Contract.Merge(m, src)
}
func (m *Contract) XXX_Size() int {
	return m.Size()
}
func (m *Contract) XXX_DiscardUnknown() {
	xxx_messageInfo_Contract.DiscardUnknown(m)
}

var xxx_messageInfo_Contract proto.InternalMessageInfo

func (m *Contract) GetMeta() *ContractMeta {
	if m != nil {
		return m.Meta
	}
	return nil
}

func (m *Contract) GetSpSignature() []byte {
	if m != nil {
		return m.SpSignature
	}
	return nil
}

func (m *Contract) GetUserSignature() []byte {
	if m != nil {
		return m.UserSignature
	}
	return nil
}

func (m *Contract) GetCreateTime() uint64 {
	if m != nil {
		return m.CreateTime
	}
	return 0
}

func (m *Contract) GetStatus() Contract_ContractStatus {
	if m != nil {
		return m.Status
	}
	return Contract_INVALID
}

type FileMetaInfo struct {
	UserId               string      `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	FileHash             string      `protobuf:"bytes,2,opt,name=file_hash,json=fileHash,proto3" json:"file_hash,omitempty"`
	FileSize             uint64      `protobuf:"varint,3,opt,name=file_size,json=fileSize,proto3" json:"file_size,omitempty"`
	ShardCount           uint64      `protobuf:"varint,4,opt,name=shard_count,json=shardCount,proto3" json:"shard_count,omitempty"`
	Contracts            []*Contract `protobuf:"bytes,5,rep,name=contracts,proto3" json:"contracts,omitempty"`
	XXX_NoUnkeyedLiteral struct{}    `json:"-"`
	XXX_unrecognized     []byte      `json:"-"`
	XXX_sizecache        int32       `json:"-"`
}

func (m *FileMetaInfo) Reset()         { *m = FileMetaInfo{} }
func (m *FileMetaInfo) String() string { return proto.CompactTextString(m) }
func (*FileMetaInfo) ProtoMessage()    {}
func (*FileMetaInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_9b47804ec1b8d60b, []int{2}
}
func (m *FileMetaInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *FileMetaInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_FileMetaInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *FileMetaInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_FileMetaInfo.Merge(m, src)
}
func (m *FileMetaInfo) XXX_Size() int {
	return m.Size()
}
func (m *FileMetaInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_FileMetaInfo.DiscardUnknown(m)
}

var xxx_messageInfo_FileMetaInfo proto.InternalMessageInfo

func (m *FileMetaInfo) GetUserId() string {
	if m != nil {
		return m.UserId
	}
	return ""
}

func (m *FileMetaInfo) GetFileHash() string {
	if m != nil {
		return m.FileHash
	}
	return ""
}

func (m *FileMetaInfo) GetFileSize() uint64 {
	if m != nil {
		return m.FileSize
	}
	return 0
}

func (m *FileMetaInfo) GetShardCount() uint64 {
	if m != nil {
		return m.ShardCount
	}
	return 0
}

func (m *FileMetaInfo) GetContracts() []*Contract {
	if m != nil {
		return m.Contracts
	}
	return nil
}

func init() {
	proto.RegisterEnum("filemeta.Contract_ContractStatus", Contract_ContractStatus_name, Contract_ContractStatus_value)
	proto.RegisterType((*ContractMeta)(nil), "filemeta.ContractMeta")
	proto.RegisterType((*Contract)(nil), "filemeta.Contract")
	proto.RegisterType((*FileMetaInfo)(nil), "filemeta.FileMetaInfo")
}

func init() { proto.RegisterFile("file_meta.proto", fileDescriptor_9b47804ec1b8d60b) }

var fileDescriptor_9b47804ec1b8d60b = []byte{
	// 534 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x64, 0x93, 0x41, 0x8f, 0xd2, 0x40,
	0x14, 0xc7, 0xb7, 0x50, 0x4a, 0xfb, 0x5a, 0x90, 0x8c, 0x66, 0x9d, 0x68, 0x44, 0x16, 0x63, 0x42,
	0x3c, 0x10, 0x83, 0x27, 0x8f, 0x2e, 0x60, 0x6c, 0xc2, 0xee, 0x9a, 0x42, 0x3c, 0x78, 0x69, 0x46,
	0x3a, 0xbb, 0x4c, 0x84, 0xb6, 0x99, 0x19, 0xa2, 0xd9, 0x4f, 0xe2, 0xd1, 0x0f, 0xe1, 0x87, 0xf0,
	0xe8, 0x47, 0x30, 0x78, 0xf2, 0x5b, 0x6c, 0xe6, 0xb5, 0x5d, 0xd8, 0xec, 0x8d, 0xf7, 0xfb, 0xff,
	0x1f, 0xcc, 0x7b, 0xef, 0x0f, 0x3c, 0xb8, 0x14, 0x6b, 0x1e, 0x6f, 0xb8, 0x66, 0xc3, 0x5c, 0x66,
	0x3a, 0x23, 0xae, 0x01, 0xa6, 0xee, 0xff, 0xaf, 0x41, 0x30, 0xce, 0x52, 0x2d, 0xd9, 0x52, 0x9f,
	0x71, 0xcd, 0xc8, 0x73, 0xf0, 0x97, 0x65, 0x1d, 0x8b, 0x84, 0x5a, 0x3d, 0x6b, 0xe0, 0x45, 0x50,
	0xa1, 0x30, 0x21, 0x0f, 0xa1, 0xa1, 0x72, 0x23, 0xd5, 0x50, 0xb2, 0x55, 0x1e, 0x26, 0xe4, 0x31,
	0x34, 0xb7, 0x8a, 0x4b, 0x83, 0xeb, 0x88, 0x1d, 0x53, 0x86, 0x09, 0x79, 0x06, 0xa0, 0x56, 0x4c,
	0x26, 0xf1, 0x8a, 0xa9, 0x15, 0xb5, 0x51, 0xf3, 0x90, 0x7c, 0x60, 0x6a, 0x65, 0x7e, 0xad, 0x90,
	0x45, 0x9a, 0xf0, 0xef, 0xb4, 0xd1, 0xb3, 0x06, 0x76, 0x54, 0x74, 0x84, 0x86, 0xec, 0xfb, 0x95,
	0xb8, 0xe6, 0xd4, 0x41, 0xbd, 0xe8, 0x9f, 0x8b, 0x6b, 0x4e, 0x1e, 0x41, 0x23, 0x97, 0x62, 0xc9,
	0x69, 0x13, 0x95, 0xa2, 0x20, 0xc7, 0xe0, 0xb0, 0x4d, 0xb6, 0x4d, 0x35, 0x75, 0x11, 0x97, 0x95,
	0x71, 0xeb, 0xec, 0x2b, 0x4f, 0xa9, 0x87, 0xef, 0x28, 0x0a, 0x72, 0x02, 0x01, 0xdb, 0xea, 0x2c,
	0x96, 0x3c, 0xe5, 0xdf, 0xd8, 0x9a, 0x42, 0xcf, 0x1a, 0xb8, 0x91, 0x6f, 0x58, 0x54, 0x20, 0xf2,
	0x02, 0x5a, 0x4a, 0x67, 0x92, 0x5d, 0xf1, 0x58, 0x69, 0x26, 0x35, 0xf5, 0xf1, 0x7b, 0x83, 0x12,
	0xce, 0x0d, 0xc3, 0x59, 0x4a, 0x13, 0x4f, 0x13, 0x1a, 0x94, 0xb3, 0x14, 0x68, 0x9a, 0x26, 0xfd,
	0x9f, 0x35, 0x70, 0xab, 0x5d, 0x93, 0x57, 0x60, 0x9b, 0x03, 0xe0, 0x82, 0xfd, 0xd1, 0xf1, 0xb0,
	0xba, 0xc8, 0xf0, 0xf0, 0x1a, 0x11, 0x7a, 0xcc, 0x0b, 0x55, 0x1e, 0x2b, 0x71, 0x95, 0x32, 0xbd,
	0x95, 0x1c, 0x37, 0x1f, 0x44, 0xbe, 0xca, 0xe7, 0x15, 0x22, 0x2f, 0xa1, 0x8d, 0x07, 0xd8, 0x9b,
	0xea, 0x68, 0x6a, 0x19, 0xba, 0xb7, 0x99, 0xeb, 0x4a, 0xce, 0x34, 0x8f, 0xb5, 0xd8, 0x70, 0xbc,
	0x87, 0x1d, 0x41, 0x81, 0x16, 0x62, 0xc3, 0xc9, 0x5b, 0x70, 0x94, 0x66, 0x7a, 0xab, 0xf0, 0x16,
	0xed, 0xd1, 0xc9, 0xfd, 0x87, 0xdd, 0x7e, 0x98, 0xa3, 0x31, 0x2a, 0x1b, 0xfa, 0xa7, 0xd0, 0xbe,
	0xab, 0x10, 0x1f, 0x9a, 0xe1, 0xf9, 0xa7, 0x77, 0xb3, 0x70, 0xd2, 0x39, 0x22, 0x2e, 0xd8, 0xe1,
	0x79, 0xb8, 0xe8, 0x58, 0xa4, 0x05, 0xde, 0xf8, 0xe2, 0xec, 0xe3, 0x6c, 0xba, 0x98, 0x4e, 0x3a,
	0x35, 0x02, 0xe0, 0x8c, 0x67, 0x17, 0xf3, 0xe9, 0xa4, 0x53, 0xef, 0xff, 0xb2, 0x20, 0x78, 0x2f,
	0xd6, 0xdc, 0x0c, 0x1f, 0xa6, 0x97, 0xd9, 0x61, 0xb0, 0xac, 0x3b, 0xc1, 0x7a, 0x0a, 0x1e, 0xa6,
	0x1a, 0x73, 0x55, 0x44, 0x11, 0x53, 0x8d, 0xb1, 0xaa, 0x44, 0x0c, 0x4d, 0x1d, 0x87, 0x44, 0x11,
	0x33, 0x73, 0x9b, 0xb9, 0x25, 0x46, 0xc4, 0x3e, 0xc8, 0xdc, 0x18, 0x63, 0xf2, 0x1a, 0xbc, 0x2a,
	0xef, 0x66, 0x0d, 0xf5, 0x81, 0x3f, 0x22, 0xf7, 0xd7, 0x10, 0xed, 0x4d, 0xa7, 0x4f, 0x7e, 0xef,
	0xba, 0xd6, 0x9f, 0x5d, 0xd7, 0xfa, 0xbb, 0xeb, 0x5a, 0x3f, 0xfe, 0x75, 0x8f, 0x3e, 0xbb, 0xc6,
	0x9b, 0x30, 0xcd, 0xbe, 0x38, 0xf8, 0x97, 0x7b, 0x73, 0x13, 0x00, 0x00, 0xff, 0xff, 0x28, 0x98,
	0x7d, 0x6e, 0x85, 0x03, 0x00, 0x00,
}

func (m *ContractMeta) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ContractMeta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ContractMeta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.StorageEnd != 0 {
		i = encodeVarintFileMeta(dAtA, i, uint64(m.StorageEnd))
		i--
		dAtA[i] = 0x60
	}
	if m.StorageStart != 0 {
		i = encodeVarintFileMeta(dAtA, i, uint64(m.StorageStart))
		i--
		dAtA[i] = 0x58
	}
	if m.AutoRenewal {
		i--
		if m.AutoRenewal {
			dAtA[i] = 1
		} else {
			dAtA[i] = 0
		}
		i--
		dAtA[i] = 0x50
	}
	if len(m.Token) > 0 {
		i -= len(m.Token)
		copy(dAtA[i:], m.Token)
		i = encodeVarintFileMeta(dAtA, i, uint64(len(m.Token)))
		i--
		dAtA[i] = 0x4a
	}
	if m.Amount != 0 {
		i = encodeVarintFileMeta(dAtA, i, uint64(m.Amount))
		i--
		dAtA[i] = 0x40
	}
	if m.Price != 0 {
		i = encodeVarintFileMeta(dAtA, i, uint64(m.Price))
		i--
		dAtA[i] = 0x38
	}
	if m.ShardSize != 0 {
		i = encodeVarintFileMeta(dAtA, i, uint64(m.ShardSize))
		i--
		dAtA[i] = 0x30
	}
	if m.ShardIndex != 0 {
		i = encodeVarintFileMeta(dAtA, i, uint64(m.ShardIndex))
		i--
		dAtA[i] = 0x28
	}
	if len(m.ShardHash) > 0 {
		i -= len(m.ShardHash)
		copy(dAtA[i:], m.ShardHash)
		i = encodeVarintFileMeta(dAtA, i, uint64(len(m.ShardHash)))
		i--
		dAtA[i] = 0x22
	}
	if len(m.UserId) > 0 {
		i -= len(m.UserId)
		copy(dAtA[i:], m.UserId)
		i = encodeVarintFileMeta(dAtA, i, uint64(len(m.UserId)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.SpId) > 0 {
		i -= len(m.SpId)
		copy(dAtA[i:], m.SpId)
		i = encodeVarintFileMeta(dAtA, i, uint64(len(m.SpId)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.ContractId) > 0 {
		i -= len(m.ContractId)
		copy(dAtA[i:], m.ContractId)
		i = encodeVarintFileMeta(dAtA, i, uint64(len(m.ContractId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Contract) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Contract) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Contract) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Status != 0 {
		i = encodeVarintFileMeta(dAtA, i, uint64(m.Status))
		i--
		dAtA[i] = 0x28
	}
	if m.CreateTime != 0 {
		i = encodeVarintFileMeta(dAtA, i, uint64(m.CreateTime))
		i--
		dAtA[i] = 0x20
	}
	if len(m.UserSignature) > 0 {
		i -= len(m.UserSignature)
		copy(dAtA[i:], m.UserSignature)
		i = encodeVarintFileMeta(dAtA, i, uint64(len(m.UserSignature)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.SpSignature) > 0 {
		i -= len(m.SpSignature)
		copy(dAtA[i:], m.SpSignature)
		i = encodeVarintFileMeta(dAtA, i, uint64(len(m.SpSignature)))
		i--
		dAtA[i] = 0x12
	}
	if m.Meta != nil {
		{
			size, err := m.Meta.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintFileMeta(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *FileMetaInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *FileMetaInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *FileMetaInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Contracts) > 0 {
		for iNdEx := len(m.Contracts) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Contracts[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintFileMeta(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0x2a
		}
	}
	if m.ShardCount != 0 {
		i = encodeVarintFileMeta(dAtA, i, uint64(m.ShardCount))
		i--
		dAtA[i] = 0x20
	}
	if m.FileSize != 0 {
		i = encodeVarintFileMeta(dAtA, i, uint64(m.FileSize))
		i--
		dAtA[i] = 0x18
	}
	if len(m.FileHash) > 0 {
		i -= len(m.FileHash)
		copy(dAtA[i:], m.FileHash)
		i = encodeVarintFileMeta(dAtA, i, uint64(len(m.FileHash)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.UserId) > 0 {
		i -= len(m.UserId)
		copy(dAtA[i:], m.UserId)
		i = encodeVarintFileMeta(dAtA, i, uint64(len(m.UserId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintFileMeta(dAtA []byte, offset int, v uint64) int {
	offset -= sovFileMeta(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func (m *ContractMeta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.ContractId)
	if l > 0 {
		n += 1 + l + sovFileMeta(uint64(l))
	}
	l = len(m.SpId)
	if l > 0 {
		n += 1 + l + sovFileMeta(uint64(l))
	}
	l = len(m.UserId)
	if l > 0 {
		n += 1 + l + sovFileMeta(uint64(l))
	}
	l = len(m.ShardHash)
	if l > 0 {
		n += 1 + l + sovFileMeta(uint64(l))
	}
	if m.ShardIndex != 0 {
		n += 1 + sovFileMeta(uint64(m.ShardIndex))
	}
	if m.ShardSize != 0 {
		n += 1 + sovFileMeta(uint64(m.ShardSize))
	}
	if m.Price != 0 {
		n += 1 + sovFileMeta(uint64(m.Price))
	}
	if m.Amount != 0 {
		n += 1 + sovFileMeta(uint64(m.Amount))
	}
	l = len(m.Token)
	if l > 0 {
		n += 1 + l + sovFileMeta(uint64(l))
	}
	if m.AutoRenewal {
		n += 2
	}
	if m.StorageStart != 0 {
		n += 1 + sovFileMeta(uint64(m.StorageStart))
	}
	if m.StorageEnd != 0 {
		n += 1 + sovFileMeta(uint64(m.StorageEnd))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Contract) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Meta != nil {
		l = m.Meta.Size()
		n += 1 + l + sovFileMeta(uint64(l))
	}
	l = len(m.SpSignature)
	if l > 0 {
		n += 1 + l + sovFileMeta(uint64(l))
	}
	l = len(m.UserSignature)
	if l > 0 {
		n += 1 + l + sovFileMeta(uint64(l))
	}
	if m.CreateTime != 0 {
		n += 1 + sovFileMeta(uint64(m.CreateTime))
	}
	if m.Status != 0 {
		n += 1 + sovFileMeta(uint64(m.Status))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *FileMetaInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.UserId)
	if l > 0 {
		n += 1 + l + sovFileMeta(uint64(l))
	}
	l = len(m.FileHash)
	if l > 0 {
		n += 1 + l + sovFileMeta(uint64(l))
	}
	if m.FileSize != 0 {
		n += 1 + sovFileMeta(uint64(m.FileSize))
	}
	if m.ShardCount != 0 {
		n += 1 + sovFileMeta(uint64(m.ShardCount))
	}
	if len(m.Contracts) > 0 {
		for _, e := range m.Contracts {
			l = e.Size()
			n += 1 + l + sovFileMeta(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovFileMeta(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozFileMeta(x uint64) (n int) {
	return sovFileMeta(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *ContractMeta) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFileMeta
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ContractMeta: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ContractMeta: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ContractId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFileMeta
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFileMeta
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ContractId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFileMeta
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFileMeta
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SpId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFileMeta
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFileMeta
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShardHash", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFileMeta
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFileMeta
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ShardHash = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShardIndex", wireType)
			}
			m.ShardIndex = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ShardIndex |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 6:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShardSize", wireType)
			}
			m.ShardSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ShardSize |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 8:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 9:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Token", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFileMeta
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFileMeta
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Token = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 10:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field AutoRenewal", wireType)
			}
			var v int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				v |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			m.AutoRenewal = bool(v != 0)
		case 11:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StorageStart", wireType)
			}
			m.StorageStart = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StorageStart |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 12:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field StorageEnd", wireType)
			}
			m.StorageEnd = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.StorageEnd |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFileMeta(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFileMeta
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Contract) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFileMeta
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Contract: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Contract: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Meta", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFileMeta
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFileMeta
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.Meta == nil {
				m.Meta = &ContractMeta{}
			}
			if err := m.Meta.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field SpSignature", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthFileMeta
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthFileMeta
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.SpSignature = append(m.SpSignature[:0], dAtA[iNdEx:postIndex]...)
			if m.SpSignature == nil {
				m.SpSignature = []byte{}
			}
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserSignature", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthFileMeta
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthFileMeta
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserSignature = append(m.UserSignature[:0], dAtA[iNdEx:postIndex]...)
			if m.UserSignature == nil {
				m.UserSignature = []byte{}
			}
			iNdEx = postIndex
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field CreateTime", wireType)
			}
			m.CreateTime = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.CreateTime |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			m.Status = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Status |= Contract_ContractStatus(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipFileMeta(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFileMeta
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *FileMetaInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowFileMeta
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: FileMetaInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: FileMetaInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field UserId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFileMeta
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFileMeta
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.UserId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileHash", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthFileMeta
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthFileMeta
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FileHash = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileSize", wireType)
			}
			m.FileSize = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.FileSize |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShardCount", wireType)
			}
			m.ShardCount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.ShardCount |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Contracts", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthFileMeta
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthFileMeta
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Contracts = append(m.Contracts, &Contract{})
			if err := m.Contracts[len(m.Contracts)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipFileMeta(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if (skippy < 0) || (iNdEx+skippy) < 0 {
				return ErrInvalidLengthFileMeta
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipFileMeta(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowFileMeta
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowFileMeta
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthFileMeta
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupFileMeta
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthFileMeta
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthFileMeta        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowFileMeta          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupFileMeta = fmt.Errorf("proto: unexpected end of group")
)
