// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: github.com/bittorrent/go-btfs/protos/wallet/wallet.proto

package walletpb

import (
	fmt "fmt"
	ledger "github.com/bittorrent/go-btfs-common/protos/ledger"
	_ "github.com/bittorrent/protobuf/gogoproto"
	proto "github.com/bittorrent/protobuf/proto"
	github_com_tron_us_protobuf_types "github.com/bittorrent/protobuf/types"
	_ "github.com/gogo/protobuf/types"
	golang_proto "github.com/golang/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
	time "time"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = golang_proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf
var _ = time.Kitchen

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type TransactionV1_Type int32

const (
	TransactionV1_EXCHANGE  TransactionV1_Type = 0
	TransactionV1_ON_CHAIN  TransactionV1_Type = 1
	TransactionV1_OFF_CHAIN TransactionV1_Type = 2
)

var TransactionV1_Type_name = map[int32]string{
	0: "EXCHANGE",
	1: "ON_CHAIN",
	2: "OFF_CHAIN",
}

var TransactionV1_Type_value = map[string]int32{
	"EXCHANGE":  0,
	"ON_CHAIN":  1,
	"OFF_CHAIN": 2,
}

func (x TransactionV1_Type) String() string {
	return proto.EnumName(TransactionV1_Type_name, int32(x))
}

func (TransactionV1_Type) EnumDescriptor() ([]byte, []int) {
	return fileDescriptor_0c953fedb813f1ad, []int{1, 0}
}

type Transaction struct {
	Id                   int64     `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" pg:"id"`
	TimeCreate           time.Time `protobuf:"bytes,2,opt,name=time_create,json=timeCreate,proto3,stdtime" json:"time_create" pg:"time_create"`
	Amount               int64     `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty" pg:"amount"`
	From                 string    `protobuf:"bytes,4,opt,name=from,proto3" json:"from,omitempty" pg:"from"`
	To                   string    `protobuf:"bytes,5,opt,name=to,proto3" json:"to,omitempty" pg:"to"`
	Status               string    `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty" pg:"status"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-" pg:"-"`
	XXX_unrecognized     []byte    `json:"-" pg:"-"`
	XXX_sizecache        int32     `json:"-" pg:"-"`
}

func (m *Transaction) Reset()         { *m = Transaction{} }
func (m *Transaction) String() string { return proto.CompactTextString(m) }
func (*Transaction) ProtoMessage()    {}
func (*Transaction) Descriptor() ([]byte, []int) {
	return fileDescriptor_0c953fedb813f1ad, []int{0}
}
func (m *Transaction) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Transaction) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Transaction.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Transaction) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Transaction.Merge(m, src)
}
func (m *Transaction) XXX_Size() int {
	return m.Size()
}
func (m *Transaction) XXX_DiscardUnknown() {
	xxx_messageInfo_Transaction.DiscardUnknown(m)
}

var xxx_messageInfo_Transaction proto.InternalMessageInfo

func (m *Transaction) GetId() int64 {
	if m != nil {
		return m.Id
	}
	return 0
}

func (m *Transaction) GetTimeCreate() time.Time {
	if m != nil {
		return m.TimeCreate
	}
	return time.Time{}
}

func (m *Transaction) GetAmount() int64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *Transaction) GetFrom() string {
	if m != nil {
		return m.From
	}
	return ""
}

func (m *Transaction) GetTo() string {
	if m != nil {
		return m.To
	}
	return ""
}

func (m *Transaction) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (*Transaction) XXX_MessageName() string {
	return "wallet.Transaction"
}

type TransactionV1 struct {
	Id                   string             `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty" pg:"id"`
	TimeCreate           time.Time          `protobuf:"bytes,2,opt,name=time_create,json=timeCreate,proto3,stdtime" json:"time_create" pg:"time_create"`
	Amount               int64              `protobuf:"varint,3,opt,name=amount,proto3" json:"amount,omitempty" pg:"amount"`
	From                 string             `protobuf:"bytes,4,opt,name=from,proto3" json:"from,omitempty" pg:"from"`
	To                   string             `protobuf:"bytes,5,opt,name=to,proto3" json:"to,omitempty" pg:"to"`
	Status               string             `protobuf:"bytes,6,opt,name=status,proto3" json:"status,omitempty" pg:"status"`
	Type                 TransactionV1_Type `protobuf:"varint,7,opt,name=type,proto3,enum=wallet.TransactionV1_Type" json:"type,omitempty" pg:"type"`
	XXX_NoUnkeyedLiteral struct{}           `json:"-" pg:"-"`
	XXX_unrecognized     []byte             `json:"-" pg:"-"`
	XXX_sizecache        int32              `json:"-" pg:"-"`
}

func (m *TransactionV1) Reset()         { *m = TransactionV1{} }
func (m *TransactionV1) String() string { return proto.CompactTextString(m) }
func (*TransactionV1) ProtoMessage()    {}
func (*TransactionV1) Descriptor() ([]byte, []int) {
	return fileDescriptor_0c953fedb813f1ad, []int{1}
}
func (m *TransactionV1) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *TransactionV1) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_TransactionV1.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *TransactionV1) XXX_Merge(src proto.Message) {
	xxx_messageInfo_TransactionV1.Merge(m, src)
}
func (m *TransactionV1) XXX_Size() int {
	return m.Size()
}
func (m *TransactionV1) XXX_DiscardUnknown() {
	xxx_messageInfo_TransactionV1.DiscardUnknown(m)
}

var xxx_messageInfo_TransactionV1 proto.InternalMessageInfo

func (m *TransactionV1) GetId() string {
	if m != nil {
		return m.Id
	}
	return ""
}

func (m *TransactionV1) GetTimeCreate() time.Time {
	if m != nil {
		return m.TimeCreate
	}
	return time.Time{}
}

func (m *TransactionV1) GetAmount() int64 {
	if m != nil {
		return m.Amount
	}
	return 0
}

func (m *TransactionV1) GetFrom() string {
	if m != nil {
		return m.From
	}
	return ""
}

func (m *TransactionV1) GetTo() string {
	if m != nil {
		return m.To
	}
	return ""
}

func (m *TransactionV1) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *TransactionV1) GetType() TransactionV1_Type {
	if m != nil {
		return m.Type
	}
	return TransactionV1_EXCHANGE
}

func (*TransactionV1) XXX_MessageName() string {
	return "wallet.TransactionV1"
}

type ChannelState struct {
	State                *ledger.ChannelState `protobuf:"bytes,1,opt,name=state,proto3" json:"state,omitempty" pg:"state"`
	TimeCreate           time.Time            `protobuf:"bytes,2,opt,name=time_create,json=timeCreate,proto3,stdtime" json:"time_create" pg:"time_create"`
	XXX_NoUnkeyedLiteral struct{}             `json:"-" pg:"-"`
	XXX_unrecognized     []byte               `json:"-" pg:"-"`
	XXX_sizecache        int32                `json:"-" pg:"-"`
}

func (m *ChannelState) Reset()         { *m = ChannelState{} }
func (m *ChannelState) String() string { return proto.CompactTextString(m) }
func (*ChannelState) ProtoMessage()    {}
func (*ChannelState) Descriptor() ([]byte, []int) {
	return fileDescriptor_0c953fedb813f1ad, []int{2}
}
func (m *ChannelState) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *ChannelState) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_ChannelState.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *ChannelState) XXX_Merge(src proto.Message) {
	xxx_messageInfo_ChannelState.Merge(m, src)
}
func (m *ChannelState) XXX_Size() int {
	return m.Size()
}
func (m *ChannelState) XXX_DiscardUnknown() {
	xxx_messageInfo_ChannelState.DiscardUnknown(m)
}

var xxx_messageInfo_ChannelState proto.InternalMessageInfo

func (m *ChannelState) GetState() *ledger.ChannelState {
	if m != nil {
		return m.State
	}
	return nil
}

func (m *ChannelState) GetTimeCreate() time.Time {
	if m != nil {
		return m.TimeCreate
	}
	return time.Time{}
}

func (*ChannelState) XXX_MessageName() string {
	return "wallet.ChannelState"
}
func init() {
	proto.RegisterEnum("wallet.TransactionV1_Type", TransactionV1_Type_name, TransactionV1_Type_value)
	golang_proto.RegisterEnum("wallet.TransactionV1_Type", TransactionV1_Type_name, TransactionV1_Type_value)
	proto.RegisterType((*Transaction)(nil), "wallet.Transaction")
	golang_proto.RegisterType((*Transaction)(nil), "wallet.Transaction")
	proto.RegisterType((*TransactionV1)(nil), "wallet.TransactionV1")
	golang_proto.RegisterType((*TransactionV1)(nil), "wallet.TransactionV1")
	proto.RegisterType((*ChannelState)(nil), "wallet.ChannelState")
	golang_proto.RegisterType((*ChannelState)(nil), "wallet.ChannelState")
}

func init() {
	proto.RegisterFile("github.com/bittorrent/go-btfs/protos/wallet/wallet.proto", fileDescriptor_0c953fedb813f1ad)
}
func init() {
	golang_proto.RegisterFile("github.com/bittorrent/go-btfs/protos/wallet/wallet.proto", fileDescriptor_0c953fedb813f1ad)
}

var fileDescriptor_0c953fedb813f1ad = []byte{
	// 452 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xcc, 0x52, 0x3d, 0x8f, 0xd3, 0x40,
	0x10, 0xbd, 0xf1, 0xe5, 0x42, 0xb2, 0xbe, 0x8b, 0x4e, 0x2b, 0x84, 0xac, 0x14, 0x4e, 0x94, 0x2a,
	0x42, 0xca, 0x9a, 0x0b, 0xa2, 0xa3, 0xb9, 0x44, 0x39, 0x8e, 0x26, 0x77, 0x32, 0x11, 0x20, 0x9a,
	0xd3, 0x3a, 0xd9, 0xf8, 0x2c, 0xd9, 0x1e, 0xcb, 0x5e, 0x0b, 0xa5, 0xe4, 0x1f, 0x50, 0xd2, 0xf0,
	0x3f, 0x28, 0x29, 0x53, 0xf2, 0x0b, 0xf8, 0x48, 0xfe, 0x04, 0x25, 0xda, 0xf5, 0x46, 0x04, 0x09,
	0x3a, 0x8a, 0xab, 0x66, 0xdf, 0xec, 0x7b, 0x6f, 0xdf, 0xac, 0x86, 0x3c, 0x09, 0x23, 0x79, 0x5b,
	0x06, 0x6c, 0x8e, 0x89, 0x27, 0x73, 0x4c, 0x07, 0x65, 0xe1, 0x85, 0x38, 0x08, 0xe4, 0xb2, 0xf0,
	0xb2, 0x1c, 0x25, 0x16, 0xde, 0x5b, 0x1e, 0xc7, 0x42, 0x9a, 0xc2, 0x74, 0x93, 0xd6, 0x2b, 0xd4,
	0x7e, 0xfa, 0x6f, 0xf9, 0x60, 0x8e, 0x49, 0x82, 0xe9, 0xce, 0x25, 0x16, 0x8b, 0x50, 0xe4, 0xa6,
	0x54, 0x2e, 0xed, 0x47, 0x7f, 0x51, 0xeb, 0x9b, 0xa0, 0x5c, 0x7a, 0x21, 0x86, 0xa8, 0x81, 0x3e,
	0x19, 0x45, 0x27, 0x44, 0x0c, 0x63, 0xf1, 0x9b, 0x25, 0xa3, 0x44, 0x14, 0x92, 0x27, 0x59, 0x45,
	0xe8, 0x7d, 0x02, 0x62, 0xcf, 0x72, 0x9e, 0x16, 0x7c, 0x2e, 0x23, 0x4c, 0x69, 0x8b, 0x58, 0xd1,
	0xc2, 0x81, 0x2e, 0xf4, 0x0f, 0x7d, 0x2b, 0x5a, 0xd0, 0x09, 0xb1, 0x95, 0xe4, 0x66, 0x9e, 0x0b,
	0x2e, 0x85, 0x63, 0x75, 0xa1, 0x6f, 0x0f, 0xdb, 0xac, 0xb2, 0x65, 0x3b, 0x5b, 0x36, 0xdb, 0xd9,
	0x8e, 0x1a, 0xeb, 0xaf, 0x9d, 0x83, 0xf7, 0xdf, 0x3a, 0xe0, 0x13, 0x25, 0x1c, 0x6b, 0x1d, 0x7d,
	0x40, 0xea, 0x3c, 0xc1, 0x32, 0x95, 0xce, 0xa1, 0xb6, 0x36, 0x88, 0x52, 0x52, 0x5b, 0xe6, 0x98,
	0x38, 0xb5, 0x2e, 0xf4, 0x9b, 0xbe, 0x3e, 0xab, 0x08, 0x12, 0x9d, 0x23, 0xdd, 0xb1, 0x24, 0x2a,
	0x6d, 0x21, 0xb9, 0x2c, 0x0b, 0xa7, 0xae, 0x7b, 0x06, 0xf5, 0x3e, 0x5a, 0xe4, 0x64, 0x2f, 0xfa,
	0xcb, 0xb3, 0xbd, 0xf0, 0xcd, 0x3b, 0x1e, 0x9e, 0x32, 0x52, 0x93, 0xab, 0x4c, 0x38, 0xf7, 0xba,
	0xd0, 0x6f, 0x0d, 0xdb, 0xcc, 0x6c, 0xcb, 0x1f, 0xf3, 0xb0, 0xd9, 0x2a, 0x13, 0xbe, 0xe6, 0xf5,
	0xce, 0x48, 0x4d, 0x21, 0x7a, 0x4c, 0x1a, 0x93, 0xd7, 0xe3, 0xcb, 0xf3, 0xe9, 0xb3, 0xc9, 0xe9,
	0x81, 0x42, 0x57, 0xd3, 0x9b, 0xf1, 0xe5, 0xf9, 0xf3, 0xe9, 0x29, 0xd0, 0x13, 0xd2, 0xbc, 0xba,
	0xb8, 0x30, 0xd0, 0xea, 0xbd, 0x03, 0x72, 0x3c, 0xbe, 0xe5, 0x69, 0x2a, 0xe2, 0x17, 0x52, 0xcd,
	0xf1, 0x90, 0x1c, 0xa9, 0xd7, 0x85, 0xfe, 0x21, 0x7b, 0x78, 0x9f, 0x99, 0xe5, 0xda, 0x27, 0xf9,
	0x15, 0xe5, 0x3f, 0x7d, 0xdd, 0x68, 0xf2, 0xf3, 0x87, 0x0b, 0xeb, 0x8d, 0x0b, 0x5f, 0x36, 0x2e,
	0x7c, 0xdf, 0xb8, 0xf0, 0x61, 0xeb, 0xc2, 0xe7, 0xad, 0x0b, 0xeb, 0xad, 0x0b, 0xa4, 0x15, 0x21,
	0x53, 0xab, 0x6f, 0xa6, 0x1f, 0xd9, 0xaf, 0x74, 0xbd, 0x56, 0xee, 0xd7, 0xf0, 0xa6, 0x51, 0xb5,
	0xb3, 0x20, 0xa8, 0xeb, 0x07, 0x1f, 0xff, 0x0a, 0x00, 0x00, 0xff, 0xff, 0x2e, 0x99, 0x27, 0x35,
	0x7e, 0x03, 0x00, 0x00,
}

func (m *Transaction) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Transaction) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Transaction) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Status) > 0 {
		i -= len(m.Status)
		copy(dAtA[i:], m.Status)
		i = encodeVarintWallet(dAtA, i, uint64(len(m.Status)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.To) > 0 {
		i -= len(m.To)
		copy(dAtA[i:], m.To)
		i = encodeVarintWallet(dAtA, i, uint64(len(m.To)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.From) > 0 {
		i -= len(m.From)
		copy(dAtA[i:], m.From)
		i = encodeVarintWallet(dAtA, i, uint64(len(m.From)))
		i--
		dAtA[i] = 0x22
	}
	if m.Amount != 0 {
		i = encodeVarintWallet(dAtA, i, uint64(m.Amount))
		i--
		dAtA[i] = 0x18
	}
	n1, err1 := github_com_tron_us_protobuf_types.StdTimeMarshalTo(m.TimeCreate, dAtA[i-github_com_tron_us_protobuf_types.SizeOfStdTime(m.TimeCreate):])
	if err1 != nil {
		return 0, err1
	}
	i -= n1
	i = encodeVarintWallet(dAtA, i, uint64(n1))
	i--
	dAtA[i] = 0x12
	if m.Id != 0 {
		i = encodeVarintWallet(dAtA, i, uint64(m.Id))
		i--
		dAtA[i] = 0x8
	}
	return len(dAtA) - i, nil
}

func (m *TransactionV1) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *TransactionV1) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *TransactionV1) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if m.Type != 0 {
		i = encodeVarintWallet(dAtA, i, uint64(m.Type))
		i--
		dAtA[i] = 0x38
	}
	if len(m.Status) > 0 {
		i -= len(m.Status)
		copy(dAtA[i:], m.Status)
		i = encodeVarintWallet(dAtA, i, uint64(len(m.Status)))
		i--
		dAtA[i] = 0x32
	}
	if len(m.To) > 0 {
		i -= len(m.To)
		copy(dAtA[i:], m.To)
		i = encodeVarintWallet(dAtA, i, uint64(len(m.To)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.From) > 0 {
		i -= len(m.From)
		copy(dAtA[i:], m.From)
		i = encodeVarintWallet(dAtA, i, uint64(len(m.From)))
		i--
		dAtA[i] = 0x22
	}
	if m.Amount != 0 {
		i = encodeVarintWallet(dAtA, i, uint64(m.Amount))
		i--
		dAtA[i] = 0x18
	}
	n2, err2 := github_com_tron_us_protobuf_types.StdTimeMarshalTo(m.TimeCreate, dAtA[i-github_com_tron_us_protobuf_types.SizeOfStdTime(m.TimeCreate):])
	if err2 != nil {
		return 0, err2
	}
	i -= n2
	i = encodeVarintWallet(dAtA, i, uint64(n2))
	i--
	dAtA[i] = 0x12
	if len(m.Id) > 0 {
		i -= len(m.Id)
		copy(dAtA[i:], m.Id)
		i = encodeVarintWallet(dAtA, i, uint64(len(m.Id)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *ChannelState) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *ChannelState) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *ChannelState) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	n3, err3 := github_com_tron_us_protobuf_types.StdTimeMarshalTo(m.TimeCreate, dAtA[i-github_com_tron_us_protobuf_types.SizeOfStdTime(m.TimeCreate):])
	if err3 != nil {
		return 0, err3
	}
	i -= n3
	i = encodeVarintWallet(dAtA, i, uint64(n3))
	i--
	dAtA[i] = 0x12
	if m.State != nil {
		{
			size, err := m.State.MarshalToSizedBuffer(dAtA[:i])
			if err != nil {
				return 0, err
			}
			i -= size
			i = encodeVarintWallet(dAtA, i, uint64(size))
		}
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintWallet(dAtA []byte, offset int, v uint64) int {
	offset -= sovWallet(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func NewPopulatedTransaction(r randyWallet, easy bool) *Transaction {
	this := &Transaction{}
	this.Id = int64(r.Int63())
	if r.Intn(2) == 0 {
		this.Id *= -1
	}
	v1 := github_com_tron_us_protobuf_types.NewPopulatedStdTime(r, easy)
	this.TimeCreate = *v1
	this.Amount = int64(r.Int63())
	if r.Intn(2) == 0 {
		this.Amount *= -1
	}
	this.From = string(randStringWallet(r))
	this.To = string(randStringWallet(r))
	this.Status = string(randStringWallet(r))
	if !easy && r.Intn(10) != 0 {
		this.XXX_unrecognized = randUnrecognizedWallet(r, 7)
	}
	return this
}

func NewPopulatedTransactionV1(r randyWallet, easy bool) *TransactionV1 {
	this := &TransactionV1{}
	this.Id = string(randStringWallet(r))
	v2 := github_com_tron_us_protobuf_types.NewPopulatedStdTime(r, easy)
	this.TimeCreate = *v2
	this.Amount = int64(r.Int63())
	if r.Intn(2) == 0 {
		this.Amount *= -1
	}
	this.From = string(randStringWallet(r))
	this.To = string(randStringWallet(r))
	this.Status = string(randStringWallet(r))
	this.Type = TransactionV1_Type([]int32{0, 1, 2}[r.Intn(3)])
	if !easy && r.Intn(10) != 0 {
		this.XXX_unrecognized = randUnrecognizedWallet(r, 8)
	}
	return this
}

func NewPopulatedChannelState(r randyWallet, easy bool) *ChannelState {
	this := &ChannelState{}
	if r.Intn(5) != 0 {
		this.State = ledger.NewPopulatedChannelState(r, easy)
	}
	v3 := github_com_tron_us_protobuf_types.NewPopulatedStdTime(r, easy)
	this.TimeCreate = *v3
	if !easy && r.Intn(10) != 0 {
		this.XXX_unrecognized = randUnrecognizedWallet(r, 3)
	}
	return this
}

type randyWallet interface {
	Float32() float32
	Float64() float64
	Int63() int64
	Int31() int32
	Uint32() uint32
	Intn(n int) int
}

func randUTF8RuneWallet(r randyWallet) rune {
	ru := r.Intn(62)
	if ru < 10 {
		return rune(ru + 48)
	} else if ru < 36 {
		return rune(ru + 55)
	}
	return rune(ru + 61)
}
func randStringWallet(r randyWallet) string {
	v4 := r.Intn(100)
	tmps := make([]rune, v4)
	for i := 0; i < v4; i++ {
		tmps[i] = randUTF8RuneWallet(r)
	}
	return string(tmps)
}
func randUnrecognizedWallet(r randyWallet, maxFieldNumber int) (dAtA []byte) {
	l := r.Intn(5)
	for i := 0; i < l; i++ {
		wire := r.Intn(4)
		if wire == 3 {
			wire = 5
		}
		fieldNumber := maxFieldNumber + r.Intn(100)
		dAtA = randFieldWallet(dAtA, r, fieldNumber, wire)
	}
	return dAtA
}
func randFieldWallet(dAtA []byte, r randyWallet, fieldNumber int, wire int) []byte {
	key := uint32(fieldNumber)<<3 | uint32(wire)
	switch wire {
	case 0:
		dAtA = encodeVarintPopulateWallet(dAtA, uint64(key))
		v5 := r.Int63()
		if r.Intn(2) == 0 {
			v5 *= -1
		}
		dAtA = encodeVarintPopulateWallet(dAtA, uint64(v5))
	case 1:
		dAtA = encodeVarintPopulateWallet(dAtA, uint64(key))
		dAtA = append(dAtA, byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)))
	case 2:
		dAtA = encodeVarintPopulateWallet(dAtA, uint64(key))
		ll := r.Intn(100)
		dAtA = encodeVarintPopulateWallet(dAtA, uint64(ll))
		for j := 0; j < ll; j++ {
			dAtA = append(dAtA, byte(r.Intn(256)))
		}
	default:
		dAtA = encodeVarintPopulateWallet(dAtA, uint64(key))
		dAtA = append(dAtA, byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)))
	}
	return dAtA
}
func encodeVarintPopulateWallet(dAtA []byte, v uint64) []byte {
	for v >= 1<<7 {
		dAtA = append(dAtA, uint8(uint64(v)&0x7f|0x80))
		v >>= 7
	}
	dAtA = append(dAtA, uint8(v))
	return dAtA
}
func (m *Transaction) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.Id != 0 {
		n += 1 + sovWallet(uint64(m.Id))
	}
	l = github_com_tron_us_protobuf_types.SizeOfStdTime(m.TimeCreate)
	n += 1 + l + sovWallet(uint64(l))
	if m.Amount != 0 {
		n += 1 + sovWallet(uint64(m.Amount))
	}
	l = len(m.From)
	if l > 0 {
		n += 1 + l + sovWallet(uint64(l))
	}
	l = len(m.To)
	if l > 0 {
		n += 1 + l + sovWallet(uint64(l))
	}
	l = len(m.Status)
	if l > 0 {
		n += 1 + l + sovWallet(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *TransactionV1) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Id)
	if l > 0 {
		n += 1 + l + sovWallet(uint64(l))
	}
	l = github_com_tron_us_protobuf_types.SizeOfStdTime(m.TimeCreate)
	n += 1 + l + sovWallet(uint64(l))
	if m.Amount != 0 {
		n += 1 + sovWallet(uint64(m.Amount))
	}
	l = len(m.From)
	if l > 0 {
		n += 1 + l + sovWallet(uint64(l))
	}
	l = len(m.To)
	if l > 0 {
		n += 1 + l + sovWallet(uint64(l))
	}
	l = len(m.Status)
	if l > 0 {
		n += 1 + l + sovWallet(uint64(l))
	}
	if m.Type != 0 {
		n += 1 + sovWallet(uint64(m.Type))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *ChannelState) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if m.State != nil {
		l = m.State.Size()
		n += 1 + l + sovWallet(uint64(l))
	}
	l = github_com_tron_us_protobuf_types.SizeOfStdTime(m.TimeCreate)
	n += 1 + l + sovWallet(uint64(l))
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovWallet(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozWallet(x uint64) (n int) {
	return sovWallet(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *Transaction) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWallet
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Transaction: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Transaction: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			m.Id = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWallet
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Id |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeCreate", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWallet
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthWallet
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthWallet
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_tron_us_protobuf_types.StdTimeUnmarshal(&m.TimeCreate, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWallet
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field From", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWallet
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWallet
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWallet
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.From = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field To", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWallet
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWallet
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWallet
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.To = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWallet
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWallet
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWallet
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Status = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWallet(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthWallet
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthWallet
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *TransactionV1) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWallet
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: TransactionV1: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: TransactionV1: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Id", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWallet
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWallet
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWallet
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Id = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeCreate", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWallet
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthWallet
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthWallet
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_tron_us_protobuf_types.StdTimeUnmarshal(&m.TimeCreate, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 3:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Amount", wireType)
			}
			m.Amount = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWallet
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Amount |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field From", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWallet
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWallet
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWallet
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.From = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field To", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWallet
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWallet
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWallet
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.To = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 6:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWallet
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthWallet
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthWallet
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Status = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 7:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Type", wireType)
			}
			m.Type = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWallet
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Type |= TransactionV1_Type(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		default:
			iNdEx = preIndex
			skippy, err := skipWallet(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthWallet
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthWallet
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *ChannelState) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowWallet
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: ChannelState: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: ChannelState: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field State", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWallet
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthWallet
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthWallet
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if m.State == nil {
				m.State = &ledger.ChannelState{}
			}
			if err := m.State.Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeCreate", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowWallet
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthWallet
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthWallet
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_tron_us_protobuf_types.StdTimeUnmarshal(&m.TimeCreate, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipWallet(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthWallet
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthWallet
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipWallet(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowWallet
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowWallet
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowWallet
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthWallet
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupWallet
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthWallet
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthWallet        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowWallet          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupWallet = fmt.Errorf("proto: unexpected end of group")
)
