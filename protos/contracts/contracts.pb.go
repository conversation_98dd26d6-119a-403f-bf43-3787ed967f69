// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: github.com/bittorrent/go-btfs/protos/contracts/contracts.proto

package contractspb

import (
	fmt "fmt"
	node "github.com/bittorrent/go-btfs-common/protos/node"
	_ "github.com/bittorrent/protobuf/gogoproto"
	proto "github.com/bittorrent/protobuf/proto"
	golang_proto "github.com/golang/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = golang_proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type Contracts struct {
	Contracts            []*node.Contracts_Contract `protobuf:"bytes,1,rep,name=contracts,proto3" json:"contracts,omitempty" pg:"contracts"`
	XXX_NoUnkeyedLiteral struct{}                   `json:"-" pg:"-"`
	XXX_unrecognized     []byte                     `json:"-" pg:"-"`
	XXX_sizecache        int32                      `json:"-" pg:"-"`
}

func (m *Contracts) Reset()         { *m = Contracts{} }
func (m *Contracts) String() string { return proto.CompactTextString(m) }
func (*Contracts) ProtoMessage()    {}
func (*Contracts) Descriptor() ([]byte, []int) {
	return fileDescriptor_c00dc19221be1ce1, []int{0}
}
func (m *Contracts) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Contracts) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Contracts.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Contracts) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Contracts.Merge(m, src)
}
func (m *Contracts) XXX_Size() int {
	return m.Size()
}
func (m *Contracts) XXX_DiscardUnknown() {
	xxx_messageInfo_Contracts.DiscardUnknown(m)
}

var xxx_messageInfo_Contracts proto.InternalMessageInfo

func (m *Contracts) GetContracts() []*node.Contracts_Contract {
	if m != nil {
		return m.Contracts
	}
	return nil
}

func (*Contracts) XXX_MessageName() string {
	return "contracts.Contracts"
}
func init() {
	proto.RegisterType((*Contracts)(nil), "contracts.Contracts")
	golang_proto.RegisterType((*Contracts)(nil), "contracts.Contracts")
}

func init() {
	proto.RegisterFile("github.com/bittorrent/go-btfs/protos/contracts/contracts.proto", fileDescriptor_c00dc19221be1ce1)
}
func init() {
	golang_proto.RegisterFile("github.com/bittorrent/go-btfs/protos/contracts/contracts.proto", fileDescriptor_c00dc19221be1ce1)
}

var fileDescriptor_c00dc19221be1ce1 = []byte{
	// 209 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0xe2, 0xb2, 0x4e, 0xcf, 0x2c, 0xc9,
	0x28, 0x4d, 0xd2, 0x4b, 0xce, 0xcf, 0xd5, 0x2f, 0x29, 0xca, 0xcf, 0xd3, 0x2d, 0x2d, 0xd6, 0x4f,
	0xcf, 0xd7, 0x4d, 0x2a, 0x49, 0x2b, 0xd6, 0x2f, 0x28, 0xca, 0x2f, 0xc9, 0x2f, 0xd6, 0x4f, 0xce,
	0xcf, 0x2b, 0x29, 0x4a, 0x4c, 0x2e, 0x41, 0x62, 0xe9, 0x81, 0xa5, 0x84, 0x38, 0xe1, 0x02, 0x52,
	0x16, 0xb8, 0xcd, 0xd1, 0x4d, 0xce, 0xcf, 0xcd, 0xcd, 0xcf, 0x83, 0x19, 0x97, 0x97, 0x9f, 0x92,
	0x0a, 0x26, 0x20, 0x86, 0x48, 0x19, 0x60, 0xd1, 0x09, 0x96, 0x49, 0x2a, 0x4d, 0xd3, 0x4f, 0xcf,
	0x4f, 0xcf, 0x07, 0x73, 0xc0, 0x2c, 0x88, 0x0e, 0x25, 0x67, 0x2e, 0x4e, 0x67, 0x98, 0xc5, 0x42,
	0x66, 0x5c, 0x08, 0x57, 0x48, 0x30, 0x2a, 0x30, 0x6b, 0x70, 0x1b, 0x49, 0xe8, 0x81, 0x8d, 0x87,
	0xab, 0x81, 0xb3, 0x82, 0x10, 0x4a, 0x9d, 0xfc, 0x7e, 0x3c, 0x94, 0x63, 0x3c, 0xf1, 0x48, 0x8e,
	0xf1, 0xc2, 0x23, 0x39, 0xc6, 0x07, 0x8f, 0xe4, 0x18, 0x67, 0x3c, 0x96, 0x63, 0x3c, 0xf0, 0x58,
	0x8e, 0xf1, 0xc4, 0x63, 0x39, 0x46, 0x2e, 0xc1, 0xcc, 0x7c, 0x3d, 0x90, 0xdb, 0xf5, 0x10, 0x1a,
	0xf8, 0xe0, 0xe6, 0x05, 0x80, 0x5c, 0x11, 0xc0, 0x18, 0xc5, 0x0d, 0x97, 0x2c, 0x48, 0x4a, 0x62,
	0x03, 0xbb, 0xcd, 0x18, 0x10, 0x00, 0x00, 0xff, 0xff, 0x37, 0xe6, 0x76, 0xf7, 0x51, 0x01, 0x00,
	0x00,
}

func (m *Contracts) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Contracts) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Contracts) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Contracts) > 0 {
		for iNdEx := len(m.Contracts) - 1; iNdEx >= 0; iNdEx-- {
			{
				size, err := m.Contracts[iNdEx].MarshalToSizedBuffer(dAtA[:i])
				if err != nil {
					return 0, err
				}
				i -= size
				i = encodeVarintContracts(dAtA, i, uint64(size))
			}
			i--
			dAtA[i] = 0xa
		}
	}
	return len(dAtA) - i, nil
}

func encodeVarintContracts(dAtA []byte, offset int, v uint64) int {
	offset -= sovContracts(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func NewPopulatedContracts(r randyContracts, easy bool) *Contracts {
	this := &Contracts{}
	if r.Intn(5) != 0 {
		v1 := r.Intn(5)
		this.Contracts = make([]*node.Contracts_Contract, v1)
		for i := 0; i < v1; i++ {
			this.Contracts[i] = node.NewPopulatedContracts_Contract(r, easy)
		}
	}
	if !easy && r.Intn(10) != 0 {
		this.XXX_unrecognized = randUnrecognizedContracts(r, 2)
	}
	return this
}

type randyContracts interface {
	Float32() float32
	Float64() float64
	Int63() int64
	Int31() int32
	Uint32() uint32
	Intn(n int) int
}

func randUTF8RuneContracts(r randyContracts) rune {
	ru := r.Intn(62)
	if ru < 10 {
		return rune(ru + 48)
	} else if ru < 36 {
		return rune(ru + 55)
	}
	return rune(ru + 61)
}
func randStringContracts(r randyContracts) string {
	v2 := r.Intn(100)
	tmps := make([]rune, v2)
	for i := 0; i < v2; i++ {
		tmps[i] = randUTF8RuneContracts(r)
	}
	return string(tmps)
}
func randUnrecognizedContracts(r randyContracts, maxFieldNumber int) (dAtA []byte) {
	l := r.Intn(5)
	for i := 0; i < l; i++ {
		wire := r.Intn(4)
		if wire == 3 {
			wire = 5
		}
		fieldNumber := maxFieldNumber + r.Intn(100)
		dAtA = randFieldContracts(dAtA, r, fieldNumber, wire)
	}
	return dAtA
}
func randFieldContracts(dAtA []byte, r randyContracts, fieldNumber int, wire int) []byte {
	key := uint32(fieldNumber)<<3 | uint32(wire)
	switch wire {
	case 0:
		dAtA = encodeVarintPopulateContracts(dAtA, uint64(key))
		v3 := r.Int63()
		if r.Intn(2) == 0 {
			v3 *= -1
		}
		dAtA = encodeVarintPopulateContracts(dAtA, uint64(v3))
	case 1:
		dAtA = encodeVarintPopulateContracts(dAtA, uint64(key))
		dAtA = append(dAtA, byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)))
	case 2:
		dAtA = encodeVarintPopulateContracts(dAtA, uint64(key))
		ll := r.Intn(100)
		dAtA = encodeVarintPopulateContracts(dAtA, uint64(ll))
		for j := 0; j < ll; j++ {
			dAtA = append(dAtA, byte(r.Intn(256)))
		}
	default:
		dAtA = encodeVarintPopulateContracts(dAtA, uint64(key))
		dAtA = append(dAtA, byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)))
	}
	return dAtA
}
func encodeVarintPopulateContracts(dAtA []byte, v uint64) []byte {
	for v >= 1<<7 {
		dAtA = append(dAtA, uint8(uint64(v)&0x7f|0x80))
		v >>= 7
	}
	dAtA = append(dAtA, uint8(v))
	return dAtA
}
func (m *Contracts) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	if len(m.Contracts) > 0 {
		for _, e := range m.Contracts {
			l = e.Size()
			n += 1 + l + sovContracts(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovContracts(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozContracts(x uint64) (n int) {
	return sovContracts(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *Contracts) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowContracts
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Contracts: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Contracts: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Contracts", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowContracts
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthContracts
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthContracts
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Contracts = append(m.Contracts, &node.Contracts_Contract{})
			if err := m.Contracts[len(m.Contracts)-1].Unmarshal(dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipContracts(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthContracts
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthContracts
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipContracts(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowContracts
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowContracts
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowContracts
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthContracts
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupContracts
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthContracts
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthContracts        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowContracts          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupContracts = fmt.Errorf("proto: unexpected end of group")
)
