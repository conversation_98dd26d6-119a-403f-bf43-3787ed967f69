syntax = "proto3";

package contracts;

// gogo plugin toggles
option (gogoproto.gogoproto_import) = true;
option (gogoproto.goproto_registration) = true;
option (gogoproto.marshaler_all) = true;
option (gogoproto.messagename_all) = true;
option (gogoproto.populate_all) = true;
option (gogoproto.sizer_all) = true;
option (gogoproto.unmarshaler_all) = true;
// golang option
option go_package = "contractspb";
// java options
option java_multiple_files = true;
option java_outer_classname = "ContractsProto";
option java_package = "io.btfs.contracts";

import "github.com/tron-us/go-btfs-common/protos/node/node.proto";
import "github.com/tron-us/protobuf/gogoproto/gogo.proto";

message Contracts {
  repeated node.Contracts.Contract contracts = 1;
}
