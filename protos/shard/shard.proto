syntax = "proto3";

package shard;

// gogo plugin toggles
option (gogoproto.gogoproto_import) = true;
option (gogoproto.goproto_registration) = true;
option (gogoproto.marshaler_all) = true;
option (gogoproto.messagename_all) = true;
option (gogoproto.populate_all) = true;
option (gogoproto.sizer_all) = true;
option (gogoproto.unmarshaler_all) = true;
// golang option
option go_package = "shardpb";
// java options
option java_multiple_files = true;
option java_outer_classname = "ShardProto";
option java_package = "io.btfs.shard";

import "github.com/tron-us/go-btfs-common/protos/guard/guard.proto";
import "github.com/tron-us/protobuf/gogoproto/gogo.proto";

message Status {
  string status = 1;
  string message = 2;
}

message AdditionalInfo {
  string info = 1;
}

message SignedContracts {
  bytes signed_escrow_contract = 1;
  guard.Contract signed_guard_contract = 2;
}
