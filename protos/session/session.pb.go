// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: github.com/bittorrent/go-btfs/protos/session/session.proto

package sessionpb

import (
	fmt "fmt"
	_ "github.com/bittorrent/protobuf/gogoproto"
	proto "github.com/bittorrent/protobuf/proto"
	github_com_tron_us_protobuf_types "github.com/bittorrent/protobuf/types"
	_ "github.com/gogo/protobuf/types"
	golang_proto "github.com/golang/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
	time "time"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = golang_proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf
var _ = time.Kitchen

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type Status struct {
	Status               string   `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty" pg:"status"`
	Message              string   `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty" pg:"message"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" pg:"-"`
	XXX_unrecognized     []byte   `json:"-" pg:"-"`
	XXX_sizecache        int32    `json:"-" pg:"-"`
}

func (m *Status) Reset()         { *m = Status{} }
func (m *Status) String() string { return proto.CompactTextString(m) }
func (*Status) ProtoMessage()    {}
func (*Status) Descriptor() ([]byte, []int) {
	return fileDescriptor_49342fb33ffe2663, []int{0}
}
func (m *Status) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Status) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Status.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Status) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Status.Merge(m, src)
}
func (m *Status) XXX_Size() int {
	return m.Size()
}
func (m *Status) XXX_DiscardUnknown() {
	xxx_messageInfo_Status.DiscardUnknown(m)
}

var xxx_messageInfo_Status proto.InternalMessageInfo

func (m *Status) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *Status) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (*Status) XXX_MessageName() string {
	return "session.Status"
}

type Metadata struct {
	TimeCreate           time.Time `protobuf:"bytes,1,opt,name=time_create,json=timeCreate,proto3,stdtime" json:"time_create" pg:"time_create"`
	RenterId             string    `protobuf:"bytes,2,opt,name=renter_id,json=renterId,proto3" json:"renter_id,omitempty" pg:"renter_id"`
	FileHash             string    `protobuf:"bytes,3,opt,name=file_hash,json=fileHash,proto3" json:"file_hash,omitempty" pg:"file_hash"`
	ShardHashes          []string  `protobuf:"bytes,4,rep,name=shard_hashes,json=shardHashes,proto3" json:"shard_hashes,omitempty" pg:"shard_hashes"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-" pg:"-"`
	XXX_unrecognized     []byte    `json:"-" pg:"-"`
	XXX_sizecache        int32     `json:"-" pg:"-"`
}

func (m *Metadata) Reset()         { *m = Metadata{} }
func (m *Metadata) String() string { return proto.CompactTextString(m) }
func (*Metadata) ProtoMessage()    {}
func (*Metadata) Descriptor() ([]byte, []int) {
	return fileDescriptor_49342fb33ffe2663, []int{1}
}
func (m *Metadata) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *Metadata) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_Metadata.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *Metadata) XXX_Merge(src proto.Message) {
	xxx_messageInfo_Metadata.Merge(m, src)
}
func (m *Metadata) XXX_Size() int {
	return m.Size()
}
func (m *Metadata) XXX_DiscardUnknown() {
	xxx_messageInfo_Metadata.DiscardUnknown(m)
}

var xxx_messageInfo_Metadata proto.InternalMessageInfo

func (m *Metadata) GetTimeCreate() time.Time {
	if m != nil {
		return m.TimeCreate
	}
	return time.Time{}
}

func (m *Metadata) GetRenterId() string {
	if m != nil {
		return m.RenterId
	}
	return ""
}

func (m *Metadata) GetFileHash() string {
	if m != nil {
		return m.FileHash
	}
	return ""
}

func (m *Metadata) GetShardHashes() []string {
	if m != nil {
		return m.ShardHashes
	}
	return nil
}

func (*Metadata) XXX_MessageName() string {
	return "session.Metadata"
}
func init() {
	proto.RegisterType((*Status)(nil), "session.Status")
	golang_proto.RegisterType((*Status)(nil), "session.Status")
	proto.RegisterType((*Metadata)(nil), "session.Metadata")
	golang_proto.RegisterType((*Metadata)(nil), "session.Metadata")
}

func init() {
	proto.RegisterFile("github.com/bittorrent/go-btfs/protos/session/session.proto", fileDescriptor_49342fb33ffe2663)
}
func init() {
	golang_proto.RegisterFile("github.com/bittorrent/go-btfs/protos/session/session.proto", fileDescriptor_49342fb33ffe2663)
}

var fileDescriptor_49342fb33ffe2663 = []byte{
	// 338 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x6c, 0x51, 0xcd, 0x4e, 0xf2, 0x40,
	0x14, 0xfd, 0xe6, 0xc3, 0x00, 0x1d, 0x48, 0x4c, 0xba, 0x30, 0x0d, 0x26, 0x03, 0xb2, 0x62, 0xc3,
	0xd4, 0xe8, 0xc2, 0xc4, 0x25, 0xc6, 0x04, 0x17, 0x26, 0x04, 0x5c, 0xb9, 0x21, 0x53, 0x3a, 0x4c,
	0x9b, 0x50, 0x86, 0xf4, 0xde, 0xbe, 0x87, 0x4b, 0x9f, 0xc2, 0x67, 0x70, 0xc9, 0xd2, 0x27, 0xf0,
	0x87, 0xbe, 0x84, 0x4b, 0xd3, 0xdb, 0x36, 0x6e, 0x5c, 0xf5, 0x9e, 0x73, 0xee, 0x39, 0xa7, 0x33,
	0xc3, 0xaf, 0x4c, 0x8c, 0x51, 0x16, 0xc8, 0x95, 0x4d, 0x7c, 0x4c, 0xed, 0x76, 0x9c, 0x81, 0x6f,
	0xec, 0x38, 0xc0, 0x35, 0xf8, 0xbb, 0xd4, 0xa2, 0x05, 0x1f, 0x34, 0x40, 0x6c, 0xb7, 0xf5, 0x57,
	0x12, 0xed, 0xb6, 0x2a, 0xd8, 0x3b, 0xff, 0x23, 0x81, 0x56, 0x82, 0x6c, 0xed, 0x1b, 0x6b, 0x2c,
	0x01, 0x9a, 0x4a, 0x6b, 0xaf, 0x6f, 0xac, 0x35, 0x1b, 0xfd, 0xbb, 0x85, 0x71, 0xa2, 0x01, 0x55,
	0xb2, 0x2b, 0x17, 0x86, 0xd7, 0xbc, 0xb9, 0x40, 0x85, 0x19, 0xb8, 0x27, 0xbc, 0x09, 0x34, 0x79,
	0x6c, 0xc0, 0x46, 0xce, 0xbc, 0x42, 0xae, 0xc7, 0x5b, 0x89, 0x06, 0x50, 0x46, 0x7b, 0xff, 0x49,
	0xa8, 0xe1, 0xf0, 0x85, 0xf1, 0xf6, 0xbd, 0x46, 0x15, 0x2a, 0x54, 0xee, 0x2d, 0xef, 0x14, 0xd9,
	0xcb, 0x55, 0xaa, 0x15, 0x6a, 0xca, 0xe8, 0x5c, 0xf4, 0x64, 0xd9, 0x2f, 0xeb, 0x7e, 0xf9, 0x50,
	0xf7, 0x4f, 0xda, 0xfb, 0xf7, 0xfe, 0xbf, 0xa7, 0x8f, 0x3e, 0x9b, 0xf3, 0xc2, 0x78, 0x43, 0x3e,
	0xf7, 0x94, 0x3b, 0xa9, 0xde, 0xa2, 0x4e, 0x97, 0x71, 0x58, 0xf5, 0xb5, 0x4b, 0xe2, 0x2e, 0x2c,
	0xc4, 0x75, 0xbc, 0xd1, 0xcb, 0x48, 0x41, 0xe4, 0x35, 0x4a, 0xb1, 0x20, 0xa6, 0x0a, 0x22, 0xf7,
	0x8c, 0x77, 0x21, 0x52, 0x69, 0x48, 0xaa, 0x06, 0xef, 0x68, 0xd0, 0x18, 0x39, 0xf3, 0x0e, 0x71,
	0x53, 0xa2, 0x26, 0xd3, 0xef, 0x2f, 0xc1, 0xf6, 0x07, 0xc1, 0xde, 0x0e, 0x82, 0x7d, 0x1e, 0x04,
	0x7b, 0xce, 0x05, 0x7b, 0xcd, 0x05, 0xdb, 0xe7, 0x82, 0xf1, 0xe3, 0xd8, 0xca, 0xe2, 0x31, 0x64,
	0x75, 0xdd, 0x93, 0xee, 0xa2, 0x1c, 0x66, 0xc5, 0xcf, 0xcf, 0xd8, 0xa3, 0x53, 0x09, 0xbb, 0x20,
	0x68, 0xd2, 0x81, 0x2e, 0x7f, 0x02, 0x00, 0x00, 0xff, 0xff, 0x1b, 0x84, 0xb5, 0xad, 0xd4, 0x01,
	0x00, 0x00,
}

func (m *Status) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Status) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Status) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Message) > 0 {
		i -= len(m.Message)
		copy(dAtA[i:], m.Message)
		i = encodeVarintSession(dAtA, i, uint64(len(m.Message)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Status) > 0 {
		i -= len(m.Status)
		copy(dAtA[i:], m.Status)
		i = encodeVarintSession(dAtA, i, uint64(len(m.Status)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *Metadata) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *Metadata) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *Metadata) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.ShardHashes) > 0 {
		for iNdEx := len(m.ShardHashes) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ShardHashes[iNdEx])
			copy(dAtA[i:], m.ShardHashes[iNdEx])
			i = encodeVarintSession(dAtA, i, uint64(len(m.ShardHashes[iNdEx])))
			i--
			dAtA[i] = 0x22
		}
	}
	if len(m.FileHash) > 0 {
		i -= len(m.FileHash)
		copy(dAtA[i:], m.FileHash)
		i = encodeVarintSession(dAtA, i, uint64(len(m.FileHash)))
		i--
		dAtA[i] = 0x1a
	}
	if len(m.RenterId) > 0 {
		i -= len(m.RenterId)
		copy(dAtA[i:], m.RenterId)
		i = encodeVarintSession(dAtA, i, uint64(len(m.RenterId)))
		i--
		dAtA[i] = 0x12
	}
	n1, err1 := github_com_tron_us_protobuf_types.StdTimeMarshalTo(m.TimeCreate, dAtA[i-github_com_tron_us_protobuf_types.SizeOfStdTime(m.TimeCreate):])
	if err1 != nil {
		return 0, err1
	}
	i -= n1
	i = encodeVarintSession(dAtA, i, uint64(n1))
	i--
	dAtA[i] = 0xa
	return len(dAtA) - i, nil
}

func encodeVarintSession(dAtA []byte, offset int, v uint64) int {
	offset -= sovSession(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func NewPopulatedStatus(r randySession, easy bool) *Status {
	this := &Status{}
	this.Status = string(randStringSession(r))
	this.Message = string(randStringSession(r))
	if !easy && r.Intn(10) != 0 {
		this.XXX_unrecognized = randUnrecognizedSession(r, 3)
	}
	return this
}

func NewPopulatedMetadata(r randySession, easy bool) *Metadata {
	this := &Metadata{}
	v1 := github_com_tron_us_protobuf_types.NewPopulatedStdTime(r, easy)
	this.TimeCreate = *v1
	this.RenterId = string(randStringSession(r))
	this.FileHash = string(randStringSession(r))
	v2 := r.Intn(10)
	this.ShardHashes = make([]string, v2)
	for i := 0; i < v2; i++ {
		this.ShardHashes[i] = string(randStringSession(r))
	}
	if !easy && r.Intn(10) != 0 {
		this.XXX_unrecognized = randUnrecognizedSession(r, 5)
	}
	return this
}

type randySession interface {
	Float32() float32
	Float64() float64
	Int63() int64
	Int31() int32
	Uint32() uint32
	Intn(n int) int
}

func randUTF8RuneSession(r randySession) rune {
	ru := r.Intn(62)
	if ru < 10 {
		return rune(ru + 48)
	} else if ru < 36 {
		return rune(ru + 55)
	}
	return rune(ru + 61)
}
func randStringSession(r randySession) string {
	v3 := r.Intn(100)
	tmps := make([]rune, v3)
	for i := 0; i < v3; i++ {
		tmps[i] = randUTF8RuneSession(r)
	}
	return string(tmps)
}
func randUnrecognizedSession(r randySession, maxFieldNumber int) (dAtA []byte) {
	l := r.Intn(5)
	for i := 0; i < l; i++ {
		wire := r.Intn(4)
		if wire == 3 {
			wire = 5
		}
		fieldNumber := maxFieldNumber + r.Intn(100)
		dAtA = randFieldSession(dAtA, r, fieldNumber, wire)
	}
	return dAtA
}
func randFieldSession(dAtA []byte, r randySession, fieldNumber int, wire int) []byte {
	key := uint32(fieldNumber)<<3 | uint32(wire)
	switch wire {
	case 0:
		dAtA = encodeVarintPopulateSession(dAtA, uint64(key))
		v4 := r.Int63()
		if r.Intn(2) == 0 {
			v4 *= -1
		}
		dAtA = encodeVarintPopulateSession(dAtA, uint64(v4))
	case 1:
		dAtA = encodeVarintPopulateSession(dAtA, uint64(key))
		dAtA = append(dAtA, byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)))
	case 2:
		dAtA = encodeVarintPopulateSession(dAtA, uint64(key))
		ll := r.Intn(100)
		dAtA = encodeVarintPopulateSession(dAtA, uint64(ll))
		for j := 0; j < ll; j++ {
			dAtA = append(dAtA, byte(r.Intn(256)))
		}
	default:
		dAtA = encodeVarintPopulateSession(dAtA, uint64(key))
		dAtA = append(dAtA, byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)))
	}
	return dAtA
}
func encodeVarintPopulateSession(dAtA []byte, v uint64) []byte {
	for v >= 1<<7 {
		dAtA = append(dAtA, uint8(uint64(v)&0x7f|0x80))
		v >>= 7
	}
	dAtA = append(dAtA, uint8(v))
	return dAtA
}
func (m *Status) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Status)
	if l > 0 {
		n += 1 + l + sovSession(uint64(l))
	}
	l = len(m.Message)
	if l > 0 {
		n += 1 + l + sovSession(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *Metadata) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = github_com_tron_us_protobuf_types.SizeOfStdTime(m.TimeCreate)
	n += 1 + l + sovSession(uint64(l))
	l = len(m.RenterId)
	if l > 0 {
		n += 1 + l + sovSession(uint64(l))
	}
	l = len(m.FileHash)
	if l > 0 {
		n += 1 + l + sovSession(uint64(l))
	}
	if len(m.ShardHashes) > 0 {
		for _, s := range m.ShardHashes {
			l = len(s)
			n += 1 + l + sovSession(uint64(l))
		}
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovSession(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozSession(x uint64) (n int) {
	return sovSession(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *Status) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSession
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Status: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Status: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Status = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSession(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSession
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthSession
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *Metadata) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowSession
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: Metadata: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: Metadata: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field TimeCreate", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthSession
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_tron_us_protobuf_types.StdTimeUnmarshal(&m.TimeCreate, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field RenterId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.RenterId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field FileHash", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.FileHash = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShardHashes", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowSession
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthSession
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthSession
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ShardHashes = append(m.ShardHashes, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipSession(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthSession
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthSession
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipSession(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowSession
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSession
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowSession
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthSession
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupSession
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthSession
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthSession        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowSession          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupSession = fmt.Errorf("proto: unexpected end of group")
)
