syntax = "proto3";

package session;

// gogo plugin toggles
option (gogoproto.gogoproto_import) = true;
option (gogoproto.goproto_registration) = true;
option (gogoproto.marshaler_all) = true;
option (gogoproto.messagename_all) = true;
option (gogoproto.populate_all) = true;
option (gogoproto.sizer_all) = true;
option (gogoproto.unmarshaler_all) = true;
// golang option
option go_package = "sessionpb";
// java options
option java_multiple_files = true;
option java_outer_classname = "SessionProto";
option java_package = "io.btfs.session";

import "github.com/tron-us/protobuf/gogoproto/gogo.proto";
import "google/protobuf/timestamp.proto";

message Status {
  string status = 1;
  string message = 2;
}

message Metadata {
  google.protobuf.Timestamp time_create = 1 [
    (gogoproto.nullable) = false,
    (gogoproto.stdtime) = true
  ];
  string renter_id = 2;
  string file_hash = 3;
  repeated string shard_hashes = 4;
}
