// Code generated by protoc-gen-gogo. DO NOT EDIT.
// source: github.com/bittorrent/go-btfs/protos/renter/renters.proto

package renterpb

import (
	fmt "fmt"
	_ "github.com/bittorrent/protobuf/gogoproto"
	proto "github.com/bittorrent/protobuf/proto"
	github_com_tron_us_protobuf_types "github.com/bittorrent/protobuf/types"
	_ "github.com/gogo/protobuf/types"
	golang_proto "github.com/golang/protobuf/proto"
	io "io"
	math "math"
	math_bits "math/bits"
	time "time"
)

// Reference imports to suppress errors if they are not otherwise used.
var _ = proto.Marshal
var _ = golang_proto.Marshal
var _ = fmt.Errorf
var _ = math.Inf
var _ = time.Kitchen

// This is a compile-time assertion to ensure that this generated file
// is compatible with the proto package it is being compiled against.
// A compilation error at this line likely means your copy of the
// proto package needs to be updated.
const _ = proto.GoGoProtoPackageIsVersion3 // please upgrade the proto package

type RenterSessionStatus struct {
	Status               string    `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty" pg:"status"`
	Message              string    `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty" pg:"message"`
	LastUpdated          time.Time `protobuf:"bytes,3,opt,name=last_updated,json=lastUpdated,proto3,stdtime" json:"last_updated" pg:"last_updated"`
	ShardHashes          []string  `protobuf:"bytes,4,rep,name=shard_hashes,json=shardHashes,proto3" json:"shard_hashes,omitempty" pg:"shard_hashes"`
	Hash                 string    `protobuf:"bytes,5,opt,name=hash,proto3" json:"hash,omitempty" pg:"hash"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-" pg:"-"`
	XXX_unrecognized     []byte    `json:"-" pg:"-"`
	XXX_sizecache        int32     `json:"-" pg:"-"`
}

func (m *RenterSessionStatus) Reset()         { *m = RenterSessionStatus{} }
func (m *RenterSessionStatus) String() string { return proto.CompactTextString(m) }
func (*RenterSessionStatus) ProtoMessage()    {}
func (*RenterSessionStatus) Descriptor() ([]byte, []int) {
	return fileDescriptor_35be7eb9adc68356, []int{0}
}
func (m *RenterSessionStatus) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RenterSessionStatus) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RenterSessionStatus.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RenterSessionStatus) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RenterSessionStatus.Merge(m, src)
}
func (m *RenterSessionStatus) XXX_Size() int {
	return m.Size()
}
func (m *RenterSessionStatus) XXX_DiscardUnknown() {
	xxx_messageInfo_RenterSessionStatus.DiscardUnknown(m)
}

var xxx_messageInfo_RenterSessionStatus proto.InternalMessageInfo

func (m *RenterSessionStatus) GetStatus() string {
	if m != nil {
		return m.Status
	}
	return ""
}

func (m *RenterSessionStatus) GetMessage() string {
	if m != nil {
		return m.Message
	}
	return ""
}

func (m *RenterSessionStatus) GetLastUpdated() time.Time {
	if m != nil {
		return m.LastUpdated
	}
	return time.Time{}
}

func (m *RenterSessionStatus) GetShardHashes() []string {
	if m != nil {
		return m.ShardHashes
	}
	return nil
}

func (m *RenterSessionStatus) GetHash() string {
	if m != nil {
		return m.Hash
	}
	return ""
}

func (*RenterSessionStatus) XXX_MessageName() string {
	return "renter.RenterSessionStatus"
}

type RenterSessionAdditionalInfo struct {
	Info                 string    `protobuf:"bytes,1,opt,name=info,proto3" json:"info,omitempty" pg:"info"`
	LastUpdated          time.Time `protobuf:"bytes,2,opt,name=last_updated,json=lastUpdated,proto3,stdtime" json:"last_updated" pg:"last_updated"`
	XXX_NoUnkeyedLiteral struct{}  `json:"-" pg:"-"`
	XXX_unrecognized     []byte    `json:"-" pg:"-"`
	XXX_sizecache        int32     `json:"-" pg:"-"`
}

func (m *RenterSessionAdditionalInfo) Reset()         { *m = RenterSessionAdditionalInfo{} }
func (m *RenterSessionAdditionalInfo) String() string { return proto.CompactTextString(m) }
func (*RenterSessionAdditionalInfo) ProtoMessage()    {}
func (*RenterSessionAdditionalInfo) Descriptor() ([]byte, []int) {
	return fileDescriptor_35be7eb9adc68356, []int{1}
}
func (m *RenterSessionAdditionalInfo) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *RenterSessionAdditionalInfo) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_RenterSessionAdditionalInfo.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *RenterSessionAdditionalInfo) XXX_Merge(src proto.Message) {
	xxx_messageInfo_RenterSessionAdditionalInfo.Merge(m, src)
}
func (m *RenterSessionAdditionalInfo) XXX_Size() int {
	return m.Size()
}
func (m *RenterSessionAdditionalInfo) XXX_DiscardUnknown() {
	xxx_messageInfo_RenterSessionAdditionalInfo.DiscardUnknown(m)
}

var xxx_messageInfo_RenterSessionAdditionalInfo proto.InternalMessageInfo

func (m *RenterSessionAdditionalInfo) GetInfo() string {
	if m != nil {
		return m.Info
	}
	return ""
}

func (m *RenterSessionAdditionalInfo) GetLastUpdated() time.Time {
	if m != nil {
		return m.LastUpdated
	}
	return time.Time{}
}

func (*RenterSessionAdditionalInfo) XXX_MessageName() string {
	return "renter.RenterSessionAdditionalInfo"
}

type OfflineMeta struct {
	OfflinePeerId        string   `protobuf:"bytes,1,opt,name=offline_peer_id,json=offlinePeerId,proto3" json:"offline_peer_id,omitempty" pg:"offline_peer_id"`
	OfflineNonceTs       uint64   `protobuf:"varint,2,opt,name=offline_nonce_ts,json=offlineNonceTs,proto3" json:"offline_nonce_ts,omitempty" pg:"offline_nonce_ts"`
	OfflineSignature     string   `protobuf:"bytes,3,opt,name=offline_signature,json=offlineSignature,proto3" json:"offline_signature,omitempty" pg:"offline_signature"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" pg:"-"`
	XXX_unrecognized     []byte   `json:"-" pg:"-"`
	XXX_sizecache        int32    `json:"-" pg:"-"`
}

func (m *OfflineMeta) Reset()         { *m = OfflineMeta{} }
func (m *OfflineMeta) String() string { return proto.CompactTextString(m) }
func (*OfflineMeta) ProtoMessage()    {}
func (*OfflineMeta) Descriptor() ([]byte, []int) {
	return fileDescriptor_35be7eb9adc68356, []int{2}
}
func (m *OfflineMeta) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OfflineMeta) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OfflineMeta.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *OfflineMeta) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfflineMeta.Merge(m, src)
}
func (m *OfflineMeta) XXX_Size() int {
	return m.Size()
}
func (m *OfflineMeta) XXX_DiscardUnknown() {
	xxx_messageInfo_OfflineMeta.DiscardUnknown(m)
}

var xxx_messageInfo_OfflineMeta proto.InternalMessageInfo

func (m *OfflineMeta) GetOfflinePeerId() string {
	if m != nil {
		return m.OfflinePeerId
	}
	return ""
}

func (m *OfflineMeta) GetOfflineNonceTs() uint64 {
	if m != nil {
		return m.OfflineNonceTs
	}
	return 0
}

func (m *OfflineMeta) GetOfflineSignature() string {
	if m != nil {
		return m.OfflineSignature
	}
	return ""
}

func (*OfflineMeta) XXX_MessageName() string {
	return "renter.OfflineMeta"
}

type OfflineSigning struct {
	Raw                  []byte   `protobuf:"bytes,1,opt,name=raw,proto3" json:"raw,omitempty" pg:"raw"`
	Price                int64    `protobuf:"varint,2,opt,name=price,proto3" json:"price,omitempty" pg:"price"`
	Sig                  []byte   `protobuf:"bytes,3,opt,name=sig,proto3" json:"sig,omitempty" pg:"sig"`
	XXX_NoUnkeyedLiteral struct{} `json:"-" pg:"-"`
	XXX_unrecognized     []byte   `json:"-" pg:"-"`
	XXX_sizecache        int32    `json:"-" pg:"-"`
}

func (m *OfflineSigning) Reset()         { *m = OfflineSigning{} }
func (m *OfflineSigning) String() string { return proto.CompactTextString(m) }
func (*OfflineSigning) ProtoMessage()    {}
func (*OfflineSigning) Descriptor() ([]byte, []int) {
	return fileDescriptor_35be7eb9adc68356, []int{3}
}
func (m *OfflineSigning) XXX_Unmarshal(b []byte) error {
	return m.Unmarshal(b)
}
func (m *OfflineSigning) XXX_Marshal(b []byte, deterministic bool) ([]byte, error) {
	if deterministic {
		return xxx_messageInfo_OfflineSigning.Marshal(b, m, deterministic)
	} else {
		b = b[:cap(b)]
		n, err := m.MarshalToSizedBuffer(b)
		if err != nil {
			return nil, err
		}
		return b[:n], nil
	}
}
func (m *OfflineSigning) XXX_Merge(src proto.Message) {
	xxx_messageInfo_OfflineSigning.Merge(m, src)
}
func (m *OfflineSigning) XXX_Size() int {
	return m.Size()
}
func (m *OfflineSigning) XXX_DiscardUnknown() {
	xxx_messageInfo_OfflineSigning.DiscardUnknown(m)
}

var xxx_messageInfo_OfflineSigning proto.InternalMessageInfo

func (m *OfflineSigning) GetRaw() []byte {
	if m != nil {
		return m.Raw
	}
	return nil
}

func (m *OfflineSigning) GetPrice() int64 {
	if m != nil {
		return m.Price
	}
	return 0
}

func (m *OfflineSigning) GetSig() []byte {
	if m != nil {
		return m.Sig
	}
	return nil
}

func (*OfflineSigning) XXX_MessageName() string {
	return "renter.OfflineSigning"
}
func init() {
	proto.RegisterType((*RenterSessionStatus)(nil), "renter.RenterSessionStatus")
	golang_proto.RegisterType((*RenterSessionStatus)(nil), "renter.RenterSessionStatus")
	proto.RegisterType((*RenterSessionAdditionalInfo)(nil), "renter.RenterSessionAdditionalInfo")
	golang_proto.RegisterType((*RenterSessionAdditionalInfo)(nil), "renter.RenterSessionAdditionalInfo")
	proto.RegisterType((*OfflineMeta)(nil), "renter.OfflineMeta")
	golang_proto.RegisterType((*OfflineMeta)(nil), "renter.OfflineMeta")
	proto.RegisterType((*OfflineSigning)(nil), "renter.OfflineSigning")
	golang_proto.RegisterType((*OfflineSigning)(nil), "renter.OfflineSigning")
}

func init() {
	proto.RegisterFile("github.com/bittorrent/go-btfs/protos/renter/renters.proto", fileDescriptor_35be7eb9adc68356)
}
func init() {
	golang_proto.RegisterFile("github.com/bittorrent/go-btfs/protos/renter/renters.proto", fileDescriptor_35be7eb9adc68356)
}

var fileDescriptor_35be7eb9adc68356 = []byte{
	// 470 bytes of a gzipped FileDescriptorProto
	0x1f, 0x8b, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x02, 0xff, 0x9c, 0x52, 0xcf, 0x8e, 0xd3, 0x3c,
	0x1c, 0xfc, 0xbc, 0xed, 0xf6, 0xdb, 0x3a, 0xa5, 0x2c, 0x06, 0xa1, 0xa8, 0x48, 0x69, 0xe9, 0x01,
	0x55, 0x42, 0x9b, 0x20, 0x90, 0xb8, 0x53, 0x09, 0xc1, 0x22, 0xc1, 0x56, 0xe9, 0x72, 0xe1, 0x12,
	0x39, 0x8d, 0xe3, 0x5a, 0x6a, 0xed, 0xc8, 0x3f, 0x47, 0x48, 0x3c, 0x03, 0x07, 0x8e, 0x3c, 0x0e,
	0x37, 0x7a, 0xe4, 0x09, 0xf8, 0xd3, 0xbe, 0x04, 0x47, 0x64, 0x3b, 0xd1, 0x0a, 0xc1, 0x89, 0x53,
	0x66, 0xc6, 0x33, 0xce, 0xf8, 0x67, 0xe3, 0xc7, 0x5c, 0x98, 0x75, 0x9d, 0xc7, 0x2b, 0xb5, 0x4d,
	0x8c, 0x56, 0xf2, 0xac, 0x86, 0x84, 0xab, 0xb3, 0xdc, 0x94, 0x90, 0x54, 0x5a, 0x19, 0x05, 0x89,
	0x66, 0xd2, 0x30, 0xdd, 0x7c, 0x20, 0x76, 0x2a, 0xe9, 0x79, 0x3a, 0x7a, 0xf0, 0x97, 0xbc, 0x73,
	0xe4, 0x75, 0x99, 0x70, 0xc5, 0x95, 0x23, 0x0e, 0xf9, 0xe4, 0x68, 0xcc, 0x95, 0xe2, 0x1b, 0x76,
	0xe5, 0x32, 0x62, 0xcb, 0xc0, 0xd0, 0x6d, 0xe5, 0x0d, 0xd3, 0xcf, 0x08, 0xdf, 0x4c, 0xdd, 0xee,
	0x4b, 0x06, 0x20, 0x94, 0x5c, 0x1a, 0x6a, 0x6a, 0x20, 0xb7, 0x71, 0x0f, 0x1c, 0x0a, 0xd1, 0x04,
	0xcd, 0xfa, 0x69, 0xc3, 0x48, 0x88, 0xff, 0xdf, 0x32, 0x00, 0xca, 0x59, 0x78, 0xe4, 0x16, 0x5a,
	0x4a, 0x9e, 0xe1, 0xc1, 0x86, 0x82, 0xc9, 0xea, 0xaa, 0xa0, 0x86, 0x15, 0x61, 0x67, 0x82, 0x66,
	0xc1, 0xc3, 0x51, 0xec, 0x1b, 0xc4, 0x6d, 0x83, 0xf8, 0xb2, 0x6d, 0x30, 0x3f, 0xd9, 0x7d, 0x1d,
	0xff, 0xf7, 0xe1, 0xdb, 0x18, 0xa5, 0x81, 0x4d, 0xbe, 0xf6, 0x41, 0x72, 0x17, 0x0f, 0x60, 0x4d,
	0x75, 0x91, 0xad, 0x29, 0xac, 0x19, 0x84, 0xdd, 0x49, 0x67, 0xd6, 0x4f, 0x03, 0xa7, 0x3d, 0x77,
	0x12, 0x21, 0xb8, 0x6b, 0x17, 0xc3, 0x63, 0x57, 0xc1, 0xe1, 0xe9, 0x3b, 0x7c, 0xe7, 0xb7, 0x83,
	0x3c, 0x29, 0x0a, 0x61, 0x84, 0x92, 0x74, 0x73, 0x2e, 0x4b, 0x65, 0x23, 0x42, 0x96, 0xaa, 0x39,
	0x8e, 0xc3, 0x7f, 0x54, 0x3e, 0xfa, 0xc7, 0xca, 0xd3, 0xf7, 0x08, 0x07, 0x17, 0x65, 0xb9, 0x11,
	0x92, 0xbd, 0x64, 0x86, 0x92, 0x7b, 0xf8, 0xba, 0xf2, 0x34, 0xab, 0x18, 0xd3, 0x99, 0x28, 0x9a,
	0xff, 0x5e, 0x6b, 0xe4, 0x05, 0x63, 0xfa, 0xbc, 0x20, 0x33, 0x7c, 0xda, 0xfa, 0xa4, 0x92, 0x2b,
	0x96, 0x19, 0x70, 0x25, 0xba, 0xe9, 0xb0, 0xd1, 0x5f, 0x59, 0xf9, 0x12, 0xc8, 0x7d, 0x7c, 0xa3,
	0x75, 0x82, 0xe0, 0x92, 0x9a, 0x5a, 0x33, 0x37, 0xe2, 0x7e, 0xda, 0x6e, 0xb1, 0x6c, 0xf5, 0xe9,
	0x0b, 0x3c, 0xbc, 0xb8, 0xd2, 0x84, 0xe4, 0xe4, 0x14, 0x77, 0x34, 0x7d, 0xeb, 0x4a, 0x0c, 0x52,
	0x0b, 0xc9, 0x2d, 0x7c, 0x5c, 0x69, 0xb1, 0xf2, 0xd7, 0xd8, 0x49, 0x3d, 0xb1, 0x3e, 0x10, 0xdc,
	0x6d, 0x3c, 0x48, 0x2d, 0x9c, 0x3f, 0xfd, 0xf9, 0x23, 0x42, 0xbb, 0x7d, 0x84, 0xbe, 0xec, 0x23,
	0xf4, 0x7d, 0x1f, 0xa1, 0x8f, 0x87, 0x08, 0x7d, 0x3a, 0x44, 0x68, 0x77, 0x88, 0x10, 0x1e, 0x0a,
	0x15, 0xdb, 0xe7, 0x1b, 0xfb, 0x17, 0x3a, 0x0f, 0xfc, 0x15, 0x2c, 0xec, 0xe4, 0x16, 0xe8, 0xcd,
	0x89, 0x97, 0xab, 0x3c, 0xef, 0xb9, 0x61, 0x3e, 0xfa, 0x15, 0x00, 0x00, 0xff, 0xff, 0x69, 0x79,
	0x96, 0xf4, 0x03, 0x03, 0x00, 0x00,
}

func (m *RenterSessionStatus) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RenterSessionStatus) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RenterSessionStatus) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Hash) > 0 {
		i -= len(m.Hash)
		copy(dAtA[i:], m.Hash)
		i = encodeVarintRenters(dAtA, i, uint64(len(m.Hash)))
		i--
		dAtA[i] = 0x2a
	}
	if len(m.ShardHashes) > 0 {
		for iNdEx := len(m.ShardHashes) - 1; iNdEx >= 0; iNdEx-- {
			i -= len(m.ShardHashes[iNdEx])
			copy(dAtA[i:], m.ShardHashes[iNdEx])
			i = encodeVarintRenters(dAtA, i, uint64(len(m.ShardHashes[iNdEx])))
			i--
			dAtA[i] = 0x22
		}
	}
	n1, err1 := github_com_tron_us_protobuf_types.StdTimeMarshalTo(m.LastUpdated, dAtA[i-github_com_tron_us_protobuf_types.SizeOfStdTime(m.LastUpdated):])
	if err1 != nil {
		return 0, err1
	}
	i -= n1
	i = encodeVarintRenters(dAtA, i, uint64(n1))
	i--
	dAtA[i] = 0x1a
	if len(m.Message) > 0 {
		i -= len(m.Message)
		copy(dAtA[i:], m.Message)
		i = encodeVarintRenters(dAtA, i, uint64(len(m.Message)))
		i--
		dAtA[i] = 0x12
	}
	if len(m.Status) > 0 {
		i -= len(m.Status)
		copy(dAtA[i:], m.Status)
		i = encodeVarintRenters(dAtA, i, uint64(len(m.Status)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *RenterSessionAdditionalInfo) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *RenterSessionAdditionalInfo) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *RenterSessionAdditionalInfo) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	n2, err2 := github_com_tron_us_protobuf_types.StdTimeMarshalTo(m.LastUpdated, dAtA[i-github_com_tron_us_protobuf_types.SizeOfStdTime(m.LastUpdated):])
	if err2 != nil {
		return 0, err2
	}
	i -= n2
	i = encodeVarintRenters(dAtA, i, uint64(n2))
	i--
	dAtA[i] = 0x12
	if len(m.Info) > 0 {
		i -= len(m.Info)
		copy(dAtA[i:], m.Info)
		i = encodeVarintRenters(dAtA, i, uint64(len(m.Info)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *OfflineMeta) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OfflineMeta) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *OfflineMeta) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.OfflineSignature) > 0 {
		i -= len(m.OfflineSignature)
		copy(dAtA[i:], m.OfflineSignature)
		i = encodeVarintRenters(dAtA, i, uint64(len(m.OfflineSignature)))
		i--
		dAtA[i] = 0x1a
	}
	if m.OfflineNonceTs != 0 {
		i = encodeVarintRenters(dAtA, i, uint64(m.OfflineNonceTs))
		i--
		dAtA[i] = 0x10
	}
	if len(m.OfflinePeerId) > 0 {
		i -= len(m.OfflinePeerId)
		copy(dAtA[i:], m.OfflinePeerId)
		i = encodeVarintRenters(dAtA, i, uint64(len(m.OfflinePeerId)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func (m *OfflineSigning) Marshal() (dAtA []byte, err error) {
	size := m.Size()
	dAtA = make([]byte, size)
	n, err := m.MarshalToSizedBuffer(dAtA[:size])
	if err != nil {
		return nil, err
	}
	return dAtA[:n], nil
}

func (m *OfflineSigning) MarshalTo(dAtA []byte) (int, error) {
	size := m.Size()
	return m.MarshalToSizedBuffer(dAtA[:size])
}

func (m *OfflineSigning) MarshalToSizedBuffer(dAtA []byte) (int, error) {
	i := len(dAtA)
	_ = i
	var l int
	_ = l
	if m.XXX_unrecognized != nil {
		i -= len(m.XXX_unrecognized)
		copy(dAtA[i:], m.XXX_unrecognized)
	}
	if len(m.Sig) > 0 {
		i -= len(m.Sig)
		copy(dAtA[i:], m.Sig)
		i = encodeVarintRenters(dAtA, i, uint64(len(m.Sig)))
		i--
		dAtA[i] = 0x1a
	}
	if m.Price != 0 {
		i = encodeVarintRenters(dAtA, i, uint64(m.Price))
		i--
		dAtA[i] = 0x10
	}
	if len(m.Raw) > 0 {
		i -= len(m.Raw)
		copy(dAtA[i:], m.Raw)
		i = encodeVarintRenters(dAtA, i, uint64(len(m.Raw)))
		i--
		dAtA[i] = 0xa
	}
	return len(dAtA) - i, nil
}

func encodeVarintRenters(dAtA []byte, offset int, v uint64) int {
	offset -= sovRenters(v)
	base := offset
	for v >= 1<<7 {
		dAtA[offset] = uint8(v&0x7f | 0x80)
		v >>= 7
		offset++
	}
	dAtA[offset] = uint8(v)
	return base
}
func NewPopulatedRenterSessionStatus(r randyRenters, easy bool) *RenterSessionStatus {
	this := &RenterSessionStatus{}
	this.Status = string(randStringRenters(r))
	this.Message = string(randStringRenters(r))
	v1 := github_com_tron_us_protobuf_types.NewPopulatedStdTime(r, easy)
	this.LastUpdated = *v1
	v2 := r.Intn(10)
	this.ShardHashes = make([]string, v2)
	for i := 0; i < v2; i++ {
		this.ShardHashes[i] = string(randStringRenters(r))
	}
	this.Hash = string(randStringRenters(r))
	if !easy && r.Intn(10) != 0 {
		this.XXX_unrecognized = randUnrecognizedRenters(r, 6)
	}
	return this
}

func NewPopulatedRenterSessionAdditionalInfo(r randyRenters, easy bool) *RenterSessionAdditionalInfo {
	this := &RenterSessionAdditionalInfo{}
	this.Info = string(randStringRenters(r))
	v3 := github_com_tron_us_protobuf_types.NewPopulatedStdTime(r, easy)
	this.LastUpdated = *v3
	if !easy && r.Intn(10) != 0 {
		this.XXX_unrecognized = randUnrecognizedRenters(r, 3)
	}
	return this
}

func NewPopulatedOfflineMeta(r randyRenters, easy bool) *OfflineMeta {
	this := &OfflineMeta{}
	this.OfflinePeerId = string(randStringRenters(r))
	this.OfflineNonceTs = uint64(uint64(r.Uint32()))
	this.OfflineSignature = string(randStringRenters(r))
	if !easy && r.Intn(10) != 0 {
		this.XXX_unrecognized = randUnrecognizedRenters(r, 4)
	}
	return this
}

func NewPopulatedOfflineSigning(r randyRenters, easy bool) *OfflineSigning {
	this := &OfflineSigning{}
	v4 := r.Intn(100)
	this.Raw = make([]byte, v4)
	for i := 0; i < v4; i++ {
		this.Raw[i] = byte(r.Intn(256))
	}
	this.Price = int64(r.Int63())
	if r.Intn(2) == 0 {
		this.Price *= -1
	}
	v5 := r.Intn(100)
	this.Sig = make([]byte, v5)
	for i := 0; i < v5; i++ {
		this.Sig[i] = byte(r.Intn(256))
	}
	if !easy && r.Intn(10) != 0 {
		this.XXX_unrecognized = randUnrecognizedRenters(r, 4)
	}
	return this
}

type randyRenters interface {
	Float32() float32
	Float64() float64
	Int63() int64
	Int31() int32
	Uint32() uint32
	Intn(n int) int
}

func randUTF8RuneRenters(r randyRenters) rune {
	ru := r.Intn(62)
	if ru < 10 {
		return rune(ru + 48)
	} else if ru < 36 {
		return rune(ru + 55)
	}
	return rune(ru + 61)
}
func randStringRenters(r randyRenters) string {
	v6 := r.Intn(100)
	tmps := make([]rune, v6)
	for i := 0; i < v6; i++ {
		tmps[i] = randUTF8RuneRenters(r)
	}
	return string(tmps)
}
func randUnrecognizedRenters(r randyRenters, maxFieldNumber int) (dAtA []byte) {
	l := r.Intn(5)
	for i := 0; i < l; i++ {
		wire := r.Intn(4)
		if wire == 3 {
			wire = 5
		}
		fieldNumber := maxFieldNumber + r.Intn(100)
		dAtA = randFieldRenters(dAtA, r, fieldNumber, wire)
	}
	return dAtA
}
func randFieldRenters(dAtA []byte, r randyRenters, fieldNumber int, wire int) []byte {
	key := uint32(fieldNumber)<<3 | uint32(wire)
	switch wire {
	case 0:
		dAtA = encodeVarintPopulateRenters(dAtA, uint64(key))
		v7 := r.Int63()
		if r.Intn(2) == 0 {
			v7 *= -1
		}
		dAtA = encodeVarintPopulateRenters(dAtA, uint64(v7))
	case 1:
		dAtA = encodeVarintPopulateRenters(dAtA, uint64(key))
		dAtA = append(dAtA, byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)))
	case 2:
		dAtA = encodeVarintPopulateRenters(dAtA, uint64(key))
		ll := r.Intn(100)
		dAtA = encodeVarintPopulateRenters(dAtA, uint64(ll))
		for j := 0; j < ll; j++ {
			dAtA = append(dAtA, byte(r.Intn(256)))
		}
	default:
		dAtA = encodeVarintPopulateRenters(dAtA, uint64(key))
		dAtA = append(dAtA, byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)), byte(r.Intn(256)))
	}
	return dAtA
}
func encodeVarintPopulateRenters(dAtA []byte, v uint64) []byte {
	for v >= 1<<7 {
		dAtA = append(dAtA, uint8(uint64(v)&0x7f|0x80))
		v >>= 7
	}
	dAtA = append(dAtA, uint8(v))
	return dAtA
}
func (m *RenterSessionStatus) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Status)
	if l > 0 {
		n += 1 + l + sovRenters(uint64(l))
	}
	l = len(m.Message)
	if l > 0 {
		n += 1 + l + sovRenters(uint64(l))
	}
	l = github_com_tron_us_protobuf_types.SizeOfStdTime(m.LastUpdated)
	n += 1 + l + sovRenters(uint64(l))
	if len(m.ShardHashes) > 0 {
		for _, s := range m.ShardHashes {
			l = len(s)
			n += 1 + l + sovRenters(uint64(l))
		}
	}
	l = len(m.Hash)
	if l > 0 {
		n += 1 + l + sovRenters(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *RenterSessionAdditionalInfo) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Info)
	if l > 0 {
		n += 1 + l + sovRenters(uint64(l))
	}
	l = github_com_tron_us_protobuf_types.SizeOfStdTime(m.LastUpdated)
	n += 1 + l + sovRenters(uint64(l))
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *OfflineMeta) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.OfflinePeerId)
	if l > 0 {
		n += 1 + l + sovRenters(uint64(l))
	}
	if m.OfflineNonceTs != 0 {
		n += 1 + sovRenters(uint64(m.OfflineNonceTs))
	}
	l = len(m.OfflineSignature)
	if l > 0 {
		n += 1 + l + sovRenters(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func (m *OfflineSigning) Size() (n int) {
	if m == nil {
		return 0
	}
	var l int
	_ = l
	l = len(m.Raw)
	if l > 0 {
		n += 1 + l + sovRenters(uint64(l))
	}
	if m.Price != 0 {
		n += 1 + sovRenters(uint64(m.Price))
	}
	l = len(m.Sig)
	if l > 0 {
		n += 1 + l + sovRenters(uint64(l))
	}
	if m.XXX_unrecognized != nil {
		n += len(m.XXX_unrecognized)
	}
	return n
}

func sovRenters(x uint64) (n int) {
	return (math_bits.Len64(x|1) + 6) / 7
}
func sozRenters(x uint64) (n int) {
	return sovRenters(uint64((x << 1) ^ uint64((int64(x) >> 63))))
}
func (m *RenterSessionStatus) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRenters
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RenterSessionStatus: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RenterSessionStatus: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Status", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRenters
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRenters
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRenters
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Status = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Message", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRenters
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRenters
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRenters
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Message = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastUpdated", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRenters
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRenters
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRenters
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_tron_us_protobuf_types.StdTimeUnmarshal(&m.LastUpdated, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		case 4:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field ShardHashes", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRenters
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRenters
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRenters
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.ShardHashes = append(m.ShardHashes, string(dAtA[iNdEx:postIndex]))
			iNdEx = postIndex
		case 5:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Hash", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRenters
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRenters
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRenters
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Hash = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRenters(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRenters
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthRenters
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *RenterSessionAdditionalInfo) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRenters
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: RenterSessionAdditionalInfo: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: RenterSessionAdditionalInfo: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Info", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRenters
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRenters
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRenters
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Info = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field LastUpdated", wireType)
			}
			var msglen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRenters
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				msglen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if msglen < 0 {
				return ErrInvalidLengthRenters
			}
			postIndex := iNdEx + msglen
			if postIndex < 0 {
				return ErrInvalidLengthRenters
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			if err := github_com_tron_us_protobuf_types.StdTimeUnmarshal(&m.LastUpdated, dAtA[iNdEx:postIndex]); err != nil {
				return err
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRenters(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRenters
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthRenters
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OfflineMeta) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRenters
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OfflineMeta: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OfflineMeta: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OfflinePeerId", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRenters
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRenters
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRenters
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OfflinePeerId = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field OfflineNonceTs", wireType)
			}
			m.OfflineNonceTs = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRenters
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.OfflineNonceTs |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field OfflineSignature", wireType)
			}
			var stringLen uint64
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRenters
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				stringLen |= uint64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			intStringLen := int(stringLen)
			if intStringLen < 0 {
				return ErrInvalidLengthRenters
			}
			postIndex := iNdEx + intStringLen
			if postIndex < 0 {
				return ErrInvalidLengthRenters
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.OfflineSignature = string(dAtA[iNdEx:postIndex])
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRenters(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRenters
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthRenters
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func (m *OfflineSigning) Unmarshal(dAtA []byte) error {
	l := len(dAtA)
	iNdEx := 0
	for iNdEx < l {
		preIndex := iNdEx
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return ErrIntOverflowRenters
			}
			if iNdEx >= l {
				return io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= uint64(b&0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		fieldNum := int32(wire >> 3)
		wireType := int(wire & 0x7)
		if wireType == 4 {
			return fmt.Errorf("proto: OfflineSigning: wiretype end group for non-group")
		}
		if fieldNum <= 0 {
			return fmt.Errorf("proto: OfflineSigning: illegal tag %d (wire type %d)", fieldNum, wire)
		}
		switch fieldNum {
		case 1:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Raw", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRenters
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRenters
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRenters
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Raw = append(m.Raw[:0], dAtA[iNdEx:postIndex]...)
			if m.Raw == nil {
				m.Raw = []byte{}
			}
			iNdEx = postIndex
		case 2:
			if wireType != 0 {
				return fmt.Errorf("proto: wrong wireType = %d for field Price", wireType)
			}
			m.Price = 0
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRenters
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				m.Price |= int64(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
		case 3:
			if wireType != 2 {
				return fmt.Errorf("proto: wrong wireType = %d for field Sig", wireType)
			}
			var byteLen int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return ErrIntOverflowRenters
				}
				if iNdEx >= l {
					return io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				byteLen |= int(b&0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if byteLen < 0 {
				return ErrInvalidLengthRenters
			}
			postIndex := iNdEx + byteLen
			if postIndex < 0 {
				return ErrInvalidLengthRenters
			}
			if postIndex > l {
				return io.ErrUnexpectedEOF
			}
			m.Sig = append(m.Sig[:0], dAtA[iNdEx:postIndex]...)
			if m.Sig == nil {
				m.Sig = []byte{}
			}
			iNdEx = postIndex
		default:
			iNdEx = preIndex
			skippy, err := skipRenters(dAtA[iNdEx:])
			if err != nil {
				return err
			}
			if skippy < 0 {
				return ErrInvalidLengthRenters
			}
			if (iNdEx + skippy) < 0 {
				return ErrInvalidLengthRenters
			}
			if (iNdEx + skippy) > l {
				return io.ErrUnexpectedEOF
			}
			m.XXX_unrecognized = append(m.XXX_unrecognized, dAtA[iNdEx:iNdEx+skippy]...)
			iNdEx += skippy
		}
	}

	if iNdEx > l {
		return io.ErrUnexpectedEOF
	}
	return nil
}
func skipRenters(dAtA []byte) (n int, err error) {
	l := len(dAtA)
	iNdEx := 0
	depth := 0
	for iNdEx < l {
		var wire uint64
		for shift := uint(0); ; shift += 7 {
			if shift >= 64 {
				return 0, ErrIntOverflowRenters
			}
			if iNdEx >= l {
				return 0, io.ErrUnexpectedEOF
			}
			b := dAtA[iNdEx]
			iNdEx++
			wire |= (uint64(b) & 0x7F) << shift
			if b < 0x80 {
				break
			}
		}
		wireType := int(wire & 0x7)
		switch wireType {
		case 0:
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRenters
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				iNdEx++
				if dAtA[iNdEx-1] < 0x80 {
					break
				}
			}
		case 1:
			iNdEx += 8
		case 2:
			var length int
			for shift := uint(0); ; shift += 7 {
				if shift >= 64 {
					return 0, ErrIntOverflowRenters
				}
				if iNdEx >= l {
					return 0, io.ErrUnexpectedEOF
				}
				b := dAtA[iNdEx]
				iNdEx++
				length |= (int(b) & 0x7F) << shift
				if b < 0x80 {
					break
				}
			}
			if length < 0 {
				return 0, ErrInvalidLengthRenters
			}
			iNdEx += length
		case 3:
			depth++
		case 4:
			if depth == 0 {
				return 0, ErrUnexpectedEndOfGroupRenters
			}
			depth--
		case 5:
			iNdEx += 4
		default:
			return 0, fmt.Errorf("proto: illegal wireType %d", wireType)
		}
		if iNdEx < 0 {
			return 0, ErrInvalidLengthRenters
		}
		if depth == 0 {
			return iNdEx, nil
		}
	}
	return 0, io.ErrUnexpectedEOF
}

var (
	ErrInvalidLengthRenters        = fmt.Errorf("proto: negative length found during unmarshaling")
	ErrIntOverflowRenters          = fmt.Errorf("proto: integer overflow")
	ErrUnexpectedEndOfGroupRenters = fmt.Errorf("proto: unexpected end of group")
)
