syntax = "proto3";

package renter;

// gogo plugin toggles
option (gogoproto.gogoproto_import) = true;
option (gogoproto.goproto_registration) = true;
option (gogoproto.marshaler_all) = true;
option (gogoproto.messagename_all) = true;
option (gogoproto.populate_all) = true;
option (gogoproto.sizer_all) = true;
option (gogoproto.unmarshaler_all) = true;
// golang option
option go_package = "renterpb";
// java options
option java_multiple_files = true;
option java_outer_classname = "RenterProto";
option java_package = "io.btfs.renter";

import "github.com/tron-us/protobuf/gogoproto/gogo.proto";
import "google/protobuf/timestamp.proto";

message RenterSessionStatus {
  string status = 1;
  string message = 2;
  google.protobuf.Timestamp last_updated = 3 [
    (gogoproto.nullable) = false,
    (gogoproto.stdtime) = true
  ];
  repeated string shard_hashes = 4;
  string hash = 5;
}

message RenterSessionAdditionalInfo {
  string info = 1;
  google.protobuf.Timestamp last_updated = 2 [
    (gogoproto.nullable) = false,
    (gogoproto.stdtime) = true
  ];
}

message OfflineMeta {
  string offline_peer_id = 1;
  uint64 offline_nonce_ts = 2;
  string offline_signature = 3;
}

message OfflineSigning {
  bytes raw = 1;
  int64 price = 2;
  bytes sig = 3;
}
