# ipfs can generate profiling dump files
*.cpuprof
*.memprof

*.swp
.ipfsconfig
*.out
*.coverprofile
*.test
*.orig
*~

coverage.txt
gx-workspace-update.json

.ipfs
bin/gx
bin/protoc-*
bin/gx*
bin/tmp
bin/gocovmerge
bin/cover


vendor
.tarball
go-ipfs-source.tar.gz
.idea/
.vscode/

docs/examples/go-ipfs-as-a-library/example-folder/Qm*
/test/sharness/t0054-dag-car-import-export-data/*.car

# ignore build output from snapcraft
/ipfs_*.snap
/parts
/stage
/prime


cmd/btfs/btfs.bk
cmd/btfs/config.yaml
cmd/btfs/config.yaml.bk
cmd/btfs/config_darwin_amd64.yaml
cmd/btfs/fs-repo-migrations
cmd/btfs/fs-repo-migrations.bk
cmd/btfs/update-darwin-amd64

cmd/.DS_Store
cmd/btfs/s
cmd/btfs/btfs
.DS_Store

cmd/btfs/btfs.upgrade
cmd/btfs/ttt
cmd/btfs/tt
cmd/btfs/t*
cmd/btfs/btfs.1.*
cmd/btfs/btfs.2.*
cmd/btfs/btfs.3.*
btfs.linux.*