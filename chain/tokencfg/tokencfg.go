package tokencfg

import (
	"fmt"
	"github.com/ethereum/go-ethereum/common"
)

const (
	TokenTypeName = "token-type"

	WBTT = "WBTT"
	TRX  = "TRX"
	USDD = "USDD"
	USDT = "USDT"
	TST  = "TST"

	// online
	bttcWBTTHex = "******************************************"
	bttcTRXHex  = "******************************************"
	bttcUSDDHex = "******************************************"
	bttcUSDTHex = "******************************************"

	// test
	bttcTestWBTTHex = "******************************************"
	bttcTestTRXHex  = "******************************************"
	bttcTestUSDDHex = "******************************************"
	bttcTestUSDTHex = "******************************************"
	bttcTestTSTHex  = "******************************************"
)

var chainIDStore int64

var MpTokenAddr map[string]common.Address
var MpTokenStr map[common.Address]string

func init() {
	MpTokenAddr = make(map[string]common.Address)
	MpTokenStr = make(map[common.Address]string)
}

func InitToken(chainID int64) {
	chainIDStore = chainID

	if chainID == 199 {
		MpTokenAddr[WBTT] = common.HexToAddress(bttcWBTTHex)
		MpTokenAddr[TRX] = common.HexToAddress(bttcTRXHex)
		MpTokenAddr[USDD] = common.HexToAddress(bttcUSDDHex)
		MpTokenAddr[USDT] = common.HexToAddress(bttcUSDTHex)

		MpTokenStr[common.HexToAddress(bttcWBTTHex)] = WBTT
		MpTokenStr[common.HexToAddress(bttcTRXHex)] = TRX
		MpTokenStr[common.HexToAddress(bttcUSDDHex)] = USDD
		MpTokenStr[common.HexToAddress(bttcUSDTHex)] = USDT
	} else {
		MpTokenAddr[WBTT] = common.HexToAddress(bttcTestWBTTHex)
		MpTokenAddr[TRX] = common.HexToAddress(bttcTestTRXHex)
		MpTokenAddr[USDD] = common.HexToAddress(bttcTestUSDDHex)
		MpTokenAddr[USDT] = common.HexToAddress(bttcTestUSDTHex)
		MpTokenAddr[TST] = common.HexToAddress(bttcTestTSTHex)

		MpTokenStr[common.HexToAddress(bttcTestWBTTHex)] = WBTT
		MpTokenStr[common.HexToAddress(bttcTestTRXHex)] = TRX
		MpTokenStr[common.HexToAddress(bttcTestUSDDHex)] = USDD
		MpTokenStr[common.HexToAddress(bttcTestUSDTHex)] = USDT
		MpTokenStr[common.HexToAddress(bttcTestTSTHex)] = TST
	}

	fmt.Println("InitToken: ", chainIDStore, MpTokenAddr)
}

func GetWbttToken() common.Address {
	//fmt.Println("------ GetWbttToken ", chainIDStore)

	if chainIDStore == 199 {
		return common.HexToAddress(bttcWBTTHex)
	} else {
		return common.HexToAddress(bttcTestWBTTHex)
	}
}

func IsWBTT(token common.Address) bool {
	return token == MpTokenAddr["WBTT"]
}

func AddToken(s string, token common.Address) string {
	if token == MpTokenAddr["WBTT"] {
		return s
	}
	return fmt.Sprintf("%s_%s", token.String(), s)
}
