package commands

import (
	"testing"
)

func TestCommandTree(t *testing.T) {
	printErrors := func(errs map[string][]error) {
		if errs == nil {
			return
		}
		t.Error("In Root command tree:")
		for cmd, err := range errs {
			t.Errorf("  In X command %s:", cmd)
			for _, e := range err {
				t.<PERSON><PERSON>("    %s", e)
			}
		}
	}
	printErrors(Root.DebugValidate())
	printErrors(RootRO.DebugValidate())
}
