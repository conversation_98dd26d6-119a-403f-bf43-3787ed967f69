{"columns": ["Name", "CI/Travis", "Coverage", "Description"], "rows": ["Libp2p", ["libp2p/go-libp2p", "go-libp2p", "p2p networking library"], ["libp2p/go-libp2p-pubsub", "go-libp2p-pubsub", "pubsub built on libp2p"], ["libp2p/go-libp2p-kad-dht", "go-libp2p-kad-dht", "dht-backed router"], ["libp2p/go-libp2p-pubsub-router", "go-libp2p-pubsub-router", "pubsub-backed router"], "Multiformats", ["ipfs/go-cid", "go-cid", "CID implementation"], ["multiformats/go-multiaddr", "go-multiaddr", "multiaddr implementation"], ["multiformats/go-multihash", "go-multihash", "multihash implementation"], ["multiformats/go-multibase", "go-multibase", "mulitbase implementation"], "Files", ["ipfs/go-unixfs", "go-unixfs", "the core 'filesystem' logic"], ["ipfs/go-mfs", "go-mfs", "a mutable filesystem editor for unixfs"], ["ipfs/go-ipfs-posinfo", "go-ipfs-posinfo", "helper datatypes for the filestore"], ["ipfs/go-ipfs-chunker", "go-ipfs-chunker", "file chunkers"], "Exchange", ["ipfs/go-ipfs-exchange-interface", "go-ipfs-exchange-interface", "exchange service interface"], ["ipfs/go-ipfs-exchange-offline", "go-ipfs-exchange-offline", "(dummy) offline implementation of the exchange service"], ["ipfs/go-bitswap", "go-bitswap", "bitswap protocol implementation"], ["ipfs/go-blockservice", "go-blockservice", "service that plugs a blockstore and an exchange together"], "Datastores", ["ipfs/go-datastore", "go-datastore", "datastore interfaces, adapters, and basic implementations"], ["ipfs/go-ipfs-ds-help", "go-ipfs-ds-help", "datastore utility functions"], ["ipfs/go-ds-flatfs", "go-ds-flatfs", "a filesystem-based datastore"], ["ipfs/go-ds-measure", "go-ds-measure", "a metric-collecting database adapter"], ["ipfs/go-ds-leveldb", "go-ds-leveldb", "a leveldb based datastore"], ["ipfs/go-ds-badger", "go-ds-badger", "a badgerdb based datastore"], "<PERSON><PERSON>", ["ipfs/go-ipns", "go-ipns", "IPNS datastructures and validation logic"], "Repo", ["ipfs/go-ipfs-config", "go-ipfs-config", "go-ipfs config file definitions"], ["ipfs/go-fs-lock", "go-fs-lock", "lockfile management functions"], ["ipfs/fs-repo-migrations", "fs-repo-migrations", "repo migrations"], "IPLD", ["ipfs/go-block-format", "go-block-format", "block interfaces and implementations"], ["ipfs/go-ipfs-blockstore", "go-ipfs-blockstore", "blockstore interfaces and implementations"], ["ipfs/go-ipld-format", "go-ipld-format", "IPLD interfaces"], ["ipfs/go-ipld-cbor", "go-ipld-cbor", "IPLD-CBOR implementation"], ["ipfs/go-ipld-git", "go-ipld-git", "IPLD-Git implementation"], ["ipfs/go-merkledag", "go-merkledag", "IPLD-Merkledag implementation (and then some)"], "Commands", ["ipfs/go-ipfs-cmds", "go-ipfs-cmds", "CLI & HTTP commands library"], ["ipfs/go-ipfs-files", "go-ipfs-files", "CLI & HTTP commands library"], ["ipfs/go-ipfs-api", "go-ipfs-api", "an old, stable shell for the IPFS HTTP API"], ["ipfs/go-ipfs-http-client", "go-ipfs-http-client", "a new, unstable shell for the IPFS HTTP API"], ["ipfs/interface-go-ipfs-core", "interface-go-ipfs-core", "core go-ipfs API interface definitions"], "Metrics & Logging", ["ipfs/go-metrics-interface", "go-metrics-interface", "metrics collection interfaces"], ["ipfs/go-metrics-prometheus", "go-metrics-prometheus", "prometheus-backed metrics collector"], ["ipfs/go-log", "go-log", "logging framework"], "Generics/Utils", ["ipfs/go-ipfs-routing", "go-ipfs-routing", "routing (content, peer, value) helpers"], ["ipfs/go-ipfs-util", "go-ipfs-util", "the kitchen sink"], ["ipfs/go-ipfs-addr", "go-ipfs-addr", "utility functions for parsing IPFS multiaddrs"]]}