package main

import (
	"testing"

	"github.com/bittorrent/go-btfs/thirdparty/assert"
)

func TestIsHidden(t *testing.T) {
	assert.True(Is<PERSON><PERSON><PERSON>("bar/.git"), t, "dirs beginning with . should be recognized as hidden")
	assert.False(<PERSON><PERSON><PERSON><PERSON>("."), t, ". for current dir should not be considered hidden")
	assert.False(<PERSON><PERSON><PERSON><PERSON>("bar/baz"), t, "normal dirs should not be hidden")
}
