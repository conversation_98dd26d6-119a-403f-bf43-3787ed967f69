name: ut

on:
  push:
  pull_request:
    branches: 
      - 'master'
      - 'release-*'

jobs:
  build-run:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Build UT docker image
        run: docker build -f Dockerfile.unit_testing -t "btfs:make_unit" .
        
      - name: Run UT
        run: docker container run -v ${{ github.workspace }}:/btfs_data --privileged --stop-timeout=300 -i btfs:make_unit
