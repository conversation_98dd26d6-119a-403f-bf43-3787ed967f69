#!/bin/sh
# Author: <PERSON> <<EMAIL>>
# MIT LICENSED

if test "$#" -lt 3
then
    echo >&2 "usage $0 FILE VALUES MSG..."
    exit 1
fi

FLAG_FILE="$1"
FLAG_VALS="$2"
shift
shift
FLAG_MSGS="$@"

test -f $FLAG_FILE || touch $FLAG_FILE

# Use x in front of tested values as flags could be
# interpreted by "test" to be for itself.
if test x"$FLAG_VALS" != x"$(cat "$FLAG_FILE")"
then
    echo "$FLAG_MSGS"
    echo "$FLAG_VALS" >"$FLAG_FILE"
fi
