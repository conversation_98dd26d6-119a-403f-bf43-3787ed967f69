data:
    build: ./data
    volumes:
        - /data
    command: sleep 1000000

bootstrap:
    build: ./bootstrap
    command: daemon --debug --init
    expose:
        - "4011"
        - "4012/udp"
    environment:
        IPFS_LOGGING: debug

server:
    build: ./server
    links:
        - bootstrap
    volumes_from:
        - data
    expose:
        - "4021"
        - "4022/udp"
    environment:
        IPFS_LOGGING: debug

client:
    build: ./client
    links:
        - bootstrap
    volumes_from:
        - data
    expose:
        - "4031"
        - "4032/udp"
    environment:
        IPFS_LOGGING: debug
