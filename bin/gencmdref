#!/usr/bin/env python

import os
import sys
import datetime
import subprocess

from subprocess import check_output

def run(cmd):
  return check_output(cmd)

def main():
  lines = [l.strip() for l in sys.stdin]

  print '# btfs command reference'
  print ''
  print 'generated on', datetime.datetime.now()
  print ''
  for line in lines:
    print '- [%s](#%s)' % (line, line.replace(' ', '-'))
  print ''

  for line in lines:
    print '## %s' % line
    print ''
    print '```'
    print run((line + ' --help').split(' ')).strip()
    print '```'
    print ''

if __name__ == '__main__':
  if '-h' in sys.argv or '--help' in sys.argv:
    print 'usage: btfs commands | %s >cmdref.md' % sys.argv[0]
    print 'outputs all commands with --help to a markdown file'
    exit(0)

  main()
