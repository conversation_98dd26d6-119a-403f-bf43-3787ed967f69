#!/bin/sh
#
# Check that the go version is at least equal to a minimum version
# number.
#
# Call it for example like this:
#
#   $ check_go_version "1.5.2"
#

USAGE="$0 GO_MIN_VERSION"

die() {
    printf >&2 "fatal: %s\n" "$@"
    exit 1
}

# Get arguments

test "$#" -eq "1" || die "This program must be passed exactly 1 arguments" "Usage: $USAGE"

GO_MIN_VERSION="$1"

UPGRADE_MSG="Please take a look at https://golang.org/doc/install to install or upgrade go."

# Get path to the directory containing this file
# If $0 has no slashes, uses "./"
PREFIX=$(expr "$0" : "\(.*\/\)") || PREFIX='./'
# Include the 'check_at_least_version' function
. ${PREFIX}check_version

# Check that the go binary exists and is in the path

GOCC=${GOCC="go"}

type ${GOCC} >/dev/null 2>&1 || die_upgrade "go is not installed or not in the PATH!"

# Check the go binary version

VERS_STR=$(${GOCC} version 2>&1) || die "'go version' failed with output: $VERS_STR"

GO_CUR_VERSION=$(expr "$VERS_STR" : ".*go version go\([^ ]*\) .*") || die "Invalid 'go version' output: $VERS_STR"

check_at_least_version "$GO_MIN_VERSION" "$GO_CUR_VERSION" "${GOCC}"
