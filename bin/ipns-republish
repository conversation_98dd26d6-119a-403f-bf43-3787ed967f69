#!/bin/bash

if [ "$#" -ne 1 ]; then
  echo "usage: $0 <btfs-or-btns-path>"
  echo "republishes an btns name every 20 minutes"
  echo "(this is an icky stop-gap until btfs nodes do it for you)"
  echo ""
  echo "example:"
  echo "  > $0 QmSYCpuKPbPQ2iFr2swJj2hvz7wQUXfPBXPiuVsQdL5FEs"
  echo ""
  exit 1
fi

# must be run online.
btfs swarm peers >/dev/null
if [ $? -ne 0 ]; then
  echo "error: btfs daemon must be online and connected to peers "
  exit 1
fi

# check the object is there
btfs object stat "$1" >/dev/null
if [ $? -ne 0 ]; then
  echo "error: btfs cannot find $1"
  exit 1
fi

echo "republishing $1 every 20 minutes"
while :
do
  btfs name publish $1
  sleep 1200
done
