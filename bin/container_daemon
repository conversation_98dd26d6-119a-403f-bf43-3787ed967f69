#!/bin/sh
set -e
user=btfs
repo="$BTFS_PATH"
bin=/usr/local/bin

if [ `id -u` -eq 0 ]; then
  echo "Changing user to $user"
  # ensure folder is writable
  su-exec "$user" test -w "$repo" || chown -R -- "$user" "$repo"
  # ensure local bin is writable (for auto-update)
  su-exec "$user" test -w "$bin" || chown -R -- "$user" "$bin"
  # restart script with new privileges
  exec su-exec "$user" "$0" "$@"
fi

# 2nd invocation with regular user
btfs version

# remove data folder to keep config consistent on restart
if [ -n "$BTFS_ENV" ]; then
  rm -rf $repo/*
fi

if [ -e "$repo/config" ]; then
  echo "Found BTFS fs-repo at $repo"
else
  case "$BTFS_PROFILE" in
    "") INIT_ARGS="" ;;
    *) INIT_ARGS="--profile=$BTFS_PROFILE" ;;
  esac

  btfs init $INIT_ARGS
  if [ "$BTFS_ENV" != "" ]; then
    btfs config Services.EscrowDomain escrow-service:50051
    btfs config Services.GuardDomain guard-interceptor-service:50051
    btfs config Services.HubDomain query-service:50051
    btfs config Services.OnlineServerDomain online-grpc-service:50051
    btfs config --bool Experimental.DisableAutoUpdate true
  fi
  if [ "$BTFS_ENV" == "dev" ] || [ "$BTFS_ENV" == "staging" ]; then
    btfs config profile apply storage-client-dev
    btfs config --bool Experimental.Analytics true
    btfs config Datastore.StorageMax 500GiB
  elif [ "$BTFS_ENV" == "production" ]; then
    btfs config profile apply storage-client
    btfs config --bool Experimental.Analytics true
    btfs config Datastore.StorageMax 10TiB
  fi
  btfs config Addresses.API /ip4/0.0.0.0/tcp/5001
  btfs config Addresses.Gateway /ip4/0.0.0.0/tcp/8080

  # Set up the swarm key, if provided

  SWARM_KEY_FILE="$repo/swarm.key"
  SWARM_KEY_PERM=0400

  # Create a swarm key from a given environment variable
  if [ ! -z "$BTFS_SWARM_KEY" ] ; then
    echo "Copying swarm key from variable..."
    echo -e "$BTFS_SWARM_KEY" >"$SWARM_KEY_FILE" || exit 1
    chmod $SWARM_KEY_PERM "$SWARM_KEY_FILE"
  fi

  # Unset the swarm key variable
  unset BTFS_SWARM_KEY

  # Check during initialization if a swarm key was provided and
  # copy it to the btfs directory with the right permissions
  # WARNING: This will replace the swarm key if it exists
  if [ ! -z "$BTFS_SWARM_KEY_FILE" ] ; then
    echo "Copying swarm key from file..."
    install -m $SWARM_KEY_PERM "$BTFS_SWARM_KEY_FILE" "$SWARM_KEY_FILE" || exit 1
  fi

  # Unset the swarm key file variable
  unset BTFS_SWARM_KEY_FILE

fi

exec btfs "$@"
